'''
Author: <PERSON><PERSON><PERSON> He
Date: 2024-10-23 10:27:35
LastEditors: <EMAIL>
LastEditTime: 2024-10-23 12:02:58
FilePath: /opr_model_v2/docs/source/conf.py
Description:

Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
'''
# Configuration file for the Sphinx documentation builder.
#
# For the full list of built-in configuration values, see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

# -- Project information -----------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#project-information

project = 'opr_model_v2'
copyright = '2024, <PERSON><PERSON><PERSON>'
author = '<PERSON><PERSON><PERSON>'
release = '2.0'

import os
import sys
sys.path.insert(0, os.path.abspath('../../scripts'))

# -- General configuration ---------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#general-configuration

extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.viewcode',
    'sphinx.ext.napoleon',  # 支持Google和NumPy风格的docstring
    'recommonmark',
    'sphinx_markdown_tables' # 支持Markdown
]

templates_path = ['_templates']
source_suffix = '.rst'
master_doc = 'index'
project = 'opr_model_v2'
copyright = '2024, Shaowen He'
author = 'Shaowen He'
version = '1.0.0'
release = '1.0.0'
language = 'zh_CN'
exclude_patterns = ['_build', 'Thumbs.db', '.DS_Store']
pygments_style = 'sphinx'


# -- Options for HTML output -------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#options-for-html-output

# html_theme = 'alabaster'
html_theme = 'sphinx_rtd_theme'
html_static_path = ['_static']
