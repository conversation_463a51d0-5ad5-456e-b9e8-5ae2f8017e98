#+TITLE: sphinx的使用说明
#+OPTIONS: ^:nil toc:t

* 源文件中添加完整的注释
- Google风格（推荐使用）
- Numpy风格
- reST风格

https://www.cnblogs.com/ryuasuka/p/11085387.html


* sphinx安装
#+begin_src shell
pip3 install sphinx
pip3 install sphinx-autobuild
pip3 install sphinx_rtd_theme  # theme
pip3 install recommonmark
pip3 install sphinx_markdown_tables
#+end_src


* 生成文档
1. sphinx-quickstart 框架生成
2. 配置conf.py，增加扩展，以及路径导入
3. 生成rst文档 =sphinx-apidoc -f -o docs .=
   1. -f 强制重写
   2. -o 输出目录
   3. . 项目目录下的所有python文件生成rst文档
   4. rst语法及目录层级
4. 自动生成并部署 =sphinx-autobuild source build/html=
   1. 直接网页访问 http://127.0.0.1:8000
   2. 命令添加到Makefile中，直接执行make autobuild即可
