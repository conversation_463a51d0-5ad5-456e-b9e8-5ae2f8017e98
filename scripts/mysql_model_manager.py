'''
Author: <PERSON><PERSON><PERSON> He
Date: 2024-11-15 11:32:20
LastEditors: <EMAIL>
LastEditTime: 2024-11-21 15:14:36
FilePath: /opr_model_v2/scripts/mysql_model_manager.py
Description: 模型管理类 - 负责模型回溯任务的数据库操作 ob-mysql

Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
'''
import sys
from utils import MySQLHelper, log_message
from const import *

class ModelManager:
    """模型管理类，负责模型回溯任务的数据库操作.

    Attributes:
        model_type: 模型类型
        mysql: MySQL连接实例
        trace_table: 回溯任务表名
        model_id: 模型ID
        rule_table: 模型规则表名
    """
    def __init__(self, model_type, model_id=None, rule_table=None, src_model_id_col=None, target_table=None, is_trace=None):
        """初始化ModelManager实例.

        Args:
            model_type: 模型类型
            model_id: 模型ID
            rule_table: 模型规则表名
            src_model_id_col: 模型ID列名
        """
        self.model_id = model_id
        self.model_type = model_type
        self.rule_table = rule_table
        # 源表的模型ID列名各不相同，作映射
        self.src_model_id_col = src_model_id_col
        # 初始化MySQL连接器, 使用阿里云内网连接
        self.mysql = MySQLHelper(**mysql_config)
        # 回溯任务表
        self.trace_table = trace_task_table
        # 目标表
        self.target_table = target_table
        # 是否为回溯任务
        self.is_trace = is_trace

    def get_trace_info(self, status):
        """获取回溯任务信息.

        Args:
            status: 任务状态码, 多个状态用逗号分隔

        Returns:
            tuple: 回溯任务ID, 模型ID, 回溯日期, 进度时间
        """
        # 将逗号分隔的状态码转化为sql in语句中的格式
        status = "'" + "','".join(status.split(',')) + "'"
        if self.model_id:
            # 指定模型获取
            sql = f"""
            select id,
                model_id,
                date_format(trace_dt, '%Y%m%d') as trace_dt,         -- 回溯日期：即回溯开始日期
                date_format(prog_tm, '%Y%m%d') as prog_tm           -- 进度时间：代表当前回溯到哪一天，若回溯结束，则等于回溯结束日期
            from {self.trace_table}
            where trace_st in ({status}) and model_id = '{self.model_id}' and lower(model_tp) = '{self.model_type}'
            """
        else:
            # 指定类型获取
            sql = f"""
            select id,
                model_id,
                date_format(trace_dt, '%Y%m%d') as trace_dt,         -- 回溯日期：即回溯开始日期
                date_format(prog_tm, '%Y%m%d') as prog_tm           -- 进度时间：代表当前回溯到哪一天，若回溯结束，则等于回溯结束日期
            from {self.trace_table}
            where trace_st in ({status}) and lower(model_tp) = '{self.model_type}'
            """
        df_trace = self.mysql.execute_query(sql)
        if df_trace:
            id = df_trace[0][0]
            model_id = df_trace[0][1]
            trace_dt = df_trace[0][2]
            prog_tm = df_trace[0][3]
            return id, model_id, trace_dt, prog_tm
        else:
            log_message(f"No trace info found for {self.model_id}.", 'info')
            return None, None, None, None

    def get_trace_st(self, key):
        """获取回溯任务状态.

        Args:
            key: 任务ID

        Returns:
            str: 任务状态码
                01-待回溯
                02-回溯中
                03-回溯完成
                04-终止中
                05-终止完成
                06-回溯失败
                07-终止失败
        """
        sql = f"""
        select trace_st from {self.trace_table} where id = '{key}'
        """
        df_status = self.mysql.execute_query(sql)
        return df_status[0][0]

    def get_modify_dt(self):
        """获取模型规则的修改日期.

        Args:
            model_id: 模型ID

        Returns:
            str: 模型规则的修改日期
        """
        # 若修改时间为空，取创建时间
        sql = f"""
        select date_format(ifnull(modi_tm, cret_tm), '%Y%m%d') as modify_dt
        from {self.rule_table}
        where {self.src_model_id_col} = '{self.model_id}'
        limit 1
        """
        df_modify_dt = self.mysql.execute_query(sql)
        return df_modify_dt[0][0]

    def update_trace_info(self, key, trace_st=None, prog_tm=None):
        """更新回溯任务状态.

        Args:
            key: 任务ID
            trace_st: 可选，回溯状态
            prog_tm: 可选，进度时间
        """
        if trace_st is not None:
            sql = f"""
            update {self.trace_table} set trace_st = '{trace_st}' where id = '{key}'
            """
            self.mysql.execute_update(sql)
        if prog_tm is not None:
            sql = f"""
            update {self.trace_table} set prog_tm = '{prog_tm}' where id = '{key}'
            """
            self.mysql.execute_update(sql)

    def update_latest_dt(self, latest_dt):
        """更新定义表最新数据日期为当前跑批日期

        Args:
            latest_dt: 最新日期，即跑批日期
        """
        sql = f"""
        update {self.rule_table} set data_latest_dt = '{latest_dt}' where {self.src_model_id_col} = '{self.model_id}'
        """
        self.mysql.execute_update(sql)
