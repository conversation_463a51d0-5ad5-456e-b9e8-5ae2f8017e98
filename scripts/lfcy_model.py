'''
Author: <PERSON><PERSON><PERSON> He
Date: 2024-10-24 14:16:18
LastEditors: <EMAIL>
LastEditTime: 2025-08-01 11:05:42
FilePath: /opr_model_v2/scripts/lfcy_model.py
Description: lfcy model (pyodps3)

Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
'''
##@resource_reference{"utils.py"}
##@resource_reference{"models.py"}
##@resource_reference{"rule_json.py"}
##@resource_reference{"rule_engine.py"}
##@resource_reference{"rule_evaluator.py"}
##@resource_reference{"mysql_model_manager.py"}
##@resource_reference{"update_valid_dates.py"}
##@resource_reference{"const.py"}
##@resource_reference{"pymysql.zip"}
import os
import sys
from odps import options
from concurrent.futures import ThreadPoolExecutor, as_completed

# 引入资源至工作空间
sys.path.append(os.path.dirname(os.path.abspath('utils.py')))
from rule_json import generate_json, LFCYProcessor
from rule_engine import RuleEngine
from rule_evaluator import LFCYEvaluator
from utils import log_message, timer, parse_json, get_yesterday_date, get_date_range_list
from mysql_model_manager import ModelManager
from update_valid_dates import UpdateValidDates
from const import *

# ODPS参数设置
options.tunnel.use_instance_tunnel = True
options.tunnel.limit_instance_tunnel = False


@timer
def process_model(model_dict, o, ds, yds, rule_ds, is_trace='0') -> dict:
    """
    处理单个模型。

    Args:
        model_dict (dict): 模型字典
        o (ODPS): ODPS 连接对象
        ds (str): 数据日期
        yds (str): 昨日数据日期
        rule_ds (str): 规则日期
        is_trace (str): 是否为回溯任务 1-是，0-否

    Returns:
        dict: 处理结果，包含success(bool)、error_message(str)、processed_date(str)
    """
    log_message(f"Processing model: {model_dict['modelId']} - {model_dict['modelNm']}", 'info')

    # 定义目标对象字典
    params = {
        'model_id': model_dict['modelId'],
        'target_table': lfcy_tables['rule_result'],
        'rule_table': lfcy_tables['src_definition'],
        'src_model_id_col': lfcy_tables['src_model_id_col'],
        'model_type': "lfcy",
        'is_trace': is_trace
    }

    try:
        # 更新失效日期
        update_valid_dates = UpdateValidDates(o, ds, **params)
        update_valid_dates.process_rule_update()
        log_message("Valid dates updated.", 'info')

        # 2. 规则指标计算
        rule_engine = RuleEngine(o, model_dict, ds, is_trace)
        # 建临时窄表 -> tmp_modelId_narrow_ds
        rule_engine.create_temp_narrow_table()
        # 生成单个模型中的所有规则的加工逻辑
        sqls = rule_engine.process_model()
        # 执行sql，存入临时窄表
        rule_engine.execute_sqls(sqls)
        log_message("Rule engine processing completed.", 'info')

        # 3. 窄表宽化 -> tmp_modelId_wide_ds
        rule_engine.create_temp_wide_table()

        # 4. 规则判断，得到目标对象 -> tmp_modelId_wide_ds & target_object
        rule_evaluator = LFCYEvaluator(o, model_dict, ds, yds, rule_ds, **params)

        # 用户群维度表 -- 过滤后的 （根据是否配置grp_id） _grp表数据 (只有生命周期模型涉及)
        # 但其他涉及目标用户的模型也需创建，rule_evaluator中统一使用_grp的表，以便通用处理 (lfcy/aarrr/rfm)
        rule_evaluator.create_obj_grp_table()

        # 建临时目标表
        rule_evaluator.create_temp_target_table()
        rule_evaluator.create_temp_target_user_table()
        # 规则判断后入临时目标表
        rule_evaluator.evaluator()
        # 写入正式目标表
        rule_evaluator.write_target_table()

        log_message("Model processing completed.", 'info')

        # 更新定义表最新数据日期
        model_manager = ModelManager(**params)
        model_manager.update_latest_dt(ds)
        log_message(f"Updating model {params['model_id']} latest data date to {ds}.", 'info')

        # 返回成功结果
        return {
            'success': True,
            'error_message': '',
            'processed_date': ds
        }

    except Exception as e:
        error_msg = f"Error processing model {model_dict['modelId']} for date {ds}: {e}"
        log_message(error_msg, 'error')
        # 返回失败结果，不再抛出异常
        return {
            'success': False,
            'error_message': str(e),
            'processed_date': ds
        }


if __name__ == "__main__":
    # 参数获取
    ds = args['ds']  # type: ignore
    yds = args['yds']  # type: ignore
    model_id = args['model_id']  # type: ignore
    # 增加是否为回溯任务参数 1-是，0-否 (默认0)
    is_trace = args['is_trace']  # type: ignore

    # 配置规则日期：总是使用最新的
    # rule_ds = get_yesterday_date()
    rule_ds = ds  # 测试阶段，使用传入日期

    if is_trace == '1':
        try:
            # 回溯任务：指定单个模型  01-待回溯 02-回溯中 03-回溯完成 04-终止中 05-终止完成 06-回溯失败 07-终止失败
            # 1. 获取回溯任务列表中的一个待回溯任务 - 每次处理一个
            model_manager = ModelManager('lfcy')
            log_message("Getting trace info", 'info')
            id, model_id, trace_dt, prog_tm = model_manager.get_trace_info('01')
            log_message(f"Trace info: {id}, {model_id}, {trace_dt}, {prog_tm}", 'info')

            if not id:
                raise Exception(f"No trace task found for model {model_id}, exiting.")

            processed_dates = set()  # 已处理日期集合
            failed_dates = {}  # 失败日期及错误信息 {date: error_message}
            successful_dates = set()  # 成功处理的日期集合

            # 为了处理最新的T-1，动态获取日期范围
            while True:
                log_message("====================================================", 'info')

                # 每次循环重新获取日期范围
                date_range_list = get_date_range_list(trace_dt)
                # 过滤已处理日期
                remaining_dates = [dt for dt in date_range_list if dt not in processed_dates]

                # 如果已处理日期与当前日期范围一致，则退出循环
                if not remaining_dates:
                    log_message(f"No remaining dates for model {model_id}, exiting loop.", 'info')
                    break

                # 获取未处理日期中的最小日期
                next_date = min(remaining_dates)
                log_message(f"Processing model: {model_id} - {next_date}", 'info')

                # 2. 先判断任务状态
                task_status = model_manager.get_trace_st(id)
                if task_status == '04':
                    # 终止中：更新状态为终止完成，且退出后续处理
                    log_message(f"Model {model_id} is terminating, skip processing.", 'info')
                    try:
                        # 更新状态为终止完成
                        model_manager.update_trace_info(id, trace_st='05')
                        log_message(f"Updating model {model_id} status to terminated completed.", 'info')
                        sys.exit(0)
                    except Exception as e:
                        # 更新状态为终止失败
                        model_manager.update_trace_info(id, trace_st='07')
                        log_message(f"Updating model {model_id} status to terminated failed: {e}", 'error')
                        sys.exit(1)

                # 当前回溯日期前一天
                trace_yds = get_yesterday_date(next_date)

                # 更新状态为回溯中
                model_manager.update_trace_info(id, trace_st='02')
                log_message(f"Updating model {model_id} status to trace in progress: {next_date}.", 'info')

                # 单个日期处理的异常处理
                try:
                    processor = LFCYProcessor(o, rule_ds, model_id) # type: ignore
                    models_df, levels_df, conditions_group, conditions = processor.get_data()
                    json_str = generate_json(models_df, levels_df, conditions_group, conditions)
                    log_message(f"json_str: {json_str}", 'info')

                    rules = parse_json(json_str)

                    # 调用处理逻辑，获取处理结果
                    result = process_model(rules['models'][0], o, next_date, trace_yds, rule_ds, is_trace)  # type: ignore

                    if result['success']:
                        # 处理成功
                        successful_dates.add(next_date)
                        log_message(f"Successfully processed model {model_id} for date {next_date}.", 'info')

                        # 更新回溯任务的当前进度时间
                        model_manager.update_trace_info(id, prog_tm=next_date)
                        log_message(f"Updating trace task {model_id} prog_tm to {next_date}.", 'info')
                    else:
                        # 处理失败，记录失败信息但继续处理其他日期
                        failed_dates[next_date] = result['error_message']
                        log_message(f"Failed to process model {model_id} for date {next_date}: {result['error_message']}", 'error')

                except Exception as e:
                    # 捕获其他可能的异常（如数据获取失败等）
                    error_msg = f"Unexpected error processing date {next_date}: {e}"
                    failed_dates[next_date] = error_msg
                    log_message(error_msg, 'error')

                # 无论成功失败，都标记为已处理
                processed_dates.add(next_date)

            # 所有回溯日期处理完成，根据成功/失败情况决定最终状态
            total_dates = len(successful_dates) + len(failed_dates)
            success_count = len(successful_dates)
            failed_count = len(failed_dates)

            log_message(f"回溯任务处理完成统计 - 总日期数: {total_dates}, 成功: {success_count}, 失败: {failed_count}", 'info')

            if failed_count > 0:
                # 记录失败日期详情
                log_message(f"失败日期详情: {failed_dates}", 'error')

            if failed_count == total_dates:
                # 所有日期都失败，标记为回溯失败
                model_manager.update_trace_info(id, trace_st='06')
                log_message(f"All dates failed for model {model_id}, updating status to trace failed.", 'error')
            elif failed_count > 0:
                # 部分失败，标记为完成但记录警告
                model_manager.update_trace_info(id, trace_st='03')
                log_message(f"Model {model_id} trace completed with {failed_count} failed dates out of {total_dates}.", 'warning')
            else:
                # 全部成功
                model_manager.update_trace_info(id, trace_st='03')
                log_message(f"All trace dates processed successfully for model {model_id}, updating status to trace completed.", 'info')

        except Exception as e:
            # 只处理初始化阶段的异常（如获取回溯任务信息失败等）
            log_message(f"初始化阶段异常，无法继续回溯任务: {e}", 'error')
            try:
                # 尝试更新状态为回溯失败
                if 'model_manager' in locals() and 'id' in locals():
                    model_manager.update_trace_info(id, trace_st='06')
                    log_message(f"Updating model {model_id} status to trace failed due to initialization error.", 'error')
            except Exception as update_error:
                log_message(f"Failed to update trace status: {update_error}", 'error')
            sys.exit(1)

    else:
        try:
            # 非回溯任务：可指定模型或全部模型
            # 1. 全量生成规则JSON对象 -> json
            processor = LFCYProcessor(o, rule_ds, model_id)  # type: ignore
            models_df, levels_df, conditions_group, conditions = processor.get_data()
            json_str = generate_json(models_df, levels_df, conditions_group, conditions)
            log_message(f"json_str: {json_str}", 'info')

            rules = parse_json(json_str)

            # 设置线程池的最大线程数：最大线程数不超过32，且不超过CPU核心数的5倍
            max_workers = min(6, (os.cpu_count() or 1) * 5)

            # 使用线程池执行器来并行处理每个model
            # 进程池有问题，卡起，can't pickle
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = [executor.submit(process_model, model_dict, o, ds, yds, rule_ds) for model_dict in rules["models"]]  # type: ignore

                # 等待所有任务完成并处理结果
                failed_models = []
                successful_models = []

                for future in as_completed(futures):
                    try:
                        result = future.result()
                        if result['success']:
                            successful_models.append(result['processed_date'])
                        else:
                            failed_models.append({
                                'date': result['processed_date'],
                                'error': result['error_message']
                            })
                    except Exception as e:
                        # 处理future.result()可能抛出的异常
                        failed_models.append({
                            'date': ds,
                            'error': str(e)
                        })
                        log_message(f"Error getting result from future: {e}", 'error')

                # 记录处理结果汇总
                total_models = len(rules["models"])
                success_count = len(successful_models)
                failed_count = len(failed_models)

                log_message(f"非回溯任务处理完成统计 - 总模型数: {total_models}, 成功: {success_count}, 失败: {failed_count}", 'info')

                if failed_count > 0:
                    log_message(f"失败模型详情: {failed_models}", 'error')
                    # 非回溯任务中有失败的模型，但不退出程序，只记录日志
                    log_message("部分模型处理失败，但非回溯任务继续完成", 'warning')

        except Exception as e:
            log_message(f"Error in main process: {e}", 'error')
            sys.exit(1)
