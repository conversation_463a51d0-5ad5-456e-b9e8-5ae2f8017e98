'''
Author: <PERSON><PERSON><PERSON> He
Date: 2024-10-14 09:31:52
LastEditors: <EMAIL>
LastEditTime: 2025-08-05 09:59:14
FilePath: /opr_model_v2/scripts/rule_evaluator.py
Description: 各个模型的等级规则依次判断，插入/更新结果数据

各模型规则判断不太一样，分别实现，通过继承RuleEvaluator实现

Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
'''
from utils import log_message, ODPSExecutor, get_date_range
from abc import ABC, abstractmethod
from mysql_model_manager import ModelManager
from const import *


class RuleEvaluator(ABC):
    """规则评估器类

    该类用于评估用户等级规则，并将结果写入目标表。

    Attributes:
        odps (ODPS): ODPS连接对象
        model_dict (dict): 模型配置字典
        ds (str): 当前日期
        yds (str): 昨日日期
        odpsex (ODPSExecutor): ODPS执行器对象
        model_id (str): 模型ID
        temp_wide_table (str): 临时宽表名
        temp_target_table (str): 当日计算结果表名
        target_table (str): 目标表名
        is_trace (int): 是否为回溯任务 1-是，0-否
    """

    def __init__(self, odps, model_dict, ds, yds, rule_ds, **kwargs):
        """初始化RuleEvaluator类

        Args:
            odps (ODPS): ODPS连接对象
            model_dict (dict): 模型配置字典
            ds (str): 当前日期
            yds (str): 昨日日期
            rule_ds (str): 规则日期
            model_type (str): 模型类型
            is_trace (str): 是否为回溯任务 1-是，0-否
            target_table (str): 目标表名
            rule_table (str): 规则表名
            src_model_id_col (str): 源表的模型ID列名
            dim_table_map (dict): 维度表映射
        """
        self.odps = odps
        self.model_dict = model_dict
        self.ds = ds
        self.yds = yds
        self.rule_ds = rule_ds
        self.kwargs = kwargs
        self.odpsex = ODPSExecutor(odps)

        # 模型ID
        self.model_id = kwargs['model_id']
        # 目标表名
        self.target_table = kwargs['target_table']
        # 规则表名
        self.rule_table = kwargs['rule_table']
        # 模型类型
        self.model_type = kwargs['model_type']
        # 源表的模型ID列名
        self.src_model_id_col = kwargs['src_model_id_col']
        # 维度表
        self.dim_table_map = dim_table_map
        # 是否为回溯任务
        self.is_trace = kwargs['is_trace']

        # 模型对象类型 (若没有，则默认为01)
        self.model_obj_cl = model_dict['modelObjCl'] if model_dict.get('modelObjCl') else '01'
        # 用户群编号
        self.grp_id = model_dict['grpId']
        # 对象表及grp表 （多个模型一起跑时防止重复，增加model_id）
        self.obj_tab = f"{self.dim_table_map[self.model_obj_cl][0]}"
        self.obj_grp_tab = f"{self.dim_table_map[self.model_obj_cl][0]}_grp_{self.model_id}"

        if self.is_trace == '1':
            # 临时宽表名
            self.temp_wide_table = f"temp_{self.model_id}_wide_{self.ds}_trace"
            # 临时窄表名 - gmv使用
            self.temp_narrow_table = f"temp_{self.model_id}_narrow_{self.ds}_trace"
            # 当日计算结果表
            self.temp_target_table = f"temp_{self.model_id}_target_{self.ds}_trace"
            # 目标用户临时表
            self.temp_target_user_table = f"temp_{self.model_id}_target_user_{self.ds}_trace"
        else:
            self.temp_wide_table = f"temp_{self.model_id}_wide_{self.ds}"
            self.temp_narrow_table = f"temp_{self.model_id}_narrow_{self.ds}"
            self.temp_target_table = f"temp_{self.model_id}_target_{self.ds}"
            self.temp_target_user_table = f"temp_{self.model_id}_target_user_{self.ds}"

    def create_obj_grp_table(self):
        """创建对象群表 （根据是否配置grp_id来筛选基础用户）-- 20250324新增

        Raises:
            SystemExit: 如果创建表失败，则退出程序。
        """
        # 限制用户群后的用户表 （替换原有用户表作为基础表）

        drop_sql = f"DROP TABLE IF EXISTS {self.obj_grp_tab}"

        if self.grp_id:
            create_sql = f"""
                CREATE TABLE IF NOT EXISTS {self.obj_grp_tab} LIFECYCLE 7 AS
                SELECT a.*
                FROM {self.obj_tab} a
                LEFT JOIN {group_view} b
                    ON b.statt_dt = '{self.ds}' and b.grp_no = '{self.grp_id}' and a.{self.dim_table_map[self.model_obj_cl][1]} = b.obj_id
                WHERE a.statt_dt = '{self.ds}' and b.obj_id IS NOT NULL
            """
        else:
            create_sql = f"""
                CREATE TABLE IF NOT EXISTS {self.obj_grp_tab} LIFECYCLE 7 AS
                SELECT a.*
                FROM {self.obj_tab} a
                WHERE a.statt_dt = '{self.ds}'
                """

        log_message("Dropping existing temporary object group table if it exists...", 'info')
        self.odpsex.execute_ddl_dml(drop_sql)

        log_message("Creating temporary object group table...", 'info')
        self.odpsex.execute_ddl_dml(create_sql)
        log_message(f"Successfully created temporary object group table: {self.obj_grp_tab}", 'info')

    def create_temp_target_table(self):
        """创建临时目标表（必须为事务表）

        Raises:
            SystemExit: 如果创建表失败，则退出程序。
        """
        drop_sql = f"DROP TABLE IF EXISTS {self.temp_target_table}"
        # 增加类型代码和描述，存放升降级类型，以及等级过期日期
        # 增加usr_num, tx_num, usr_lt, statt_dt - ltv
        create_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.temp_target_table} (
                obj_id string,
                level_code string,
                chg_tp string,
                valid_date string,
                level_value string,
                usr_num bigint,
                tx_num bigint,
                usr_lt double,
                statt_dt string
            ) TBLPROPERTIES("transactional"="true")
            LIFECYCLE 7
        """
        log_message("Dropping existing temporary target table if it exists...", 'info')
        self.odpsex.execute_ddl_dml(drop_sql)

        log_message("Creating temporary target table...", 'info')
        self.odpsex.execute_ddl_dml(create_sql)
        log_message(f"Successfully created temporary target table: {self.temp_target_table}", 'info')

    def create_temp_target_user_table(self):
        """创建临时目标用户表

        Raises:
            SystemExit: 如果创建表失败，则退出程序。
        """
        drop_sql = f"DROP TABLE IF EXISTS {self.temp_target_user_table}"

        # 保级或降级的目标用户，即obj_tp='02'，将昨日的有效日期带过来判断
        create_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.temp_target_user_table} (
                obj_id string,
                level_code string,
                obj_tp string,
                valid_date string
            )
            LIFECYCLE 7
        """
        log_message("Dropping existing temporary target user table if it exists...", 'info')
        self.odpsex.execute_ddl_dml(drop_sql)

        log_message("Creating temporary target user table...", 'info')
        self.odpsex.execute_ddl_dml(create_sql)
        log_message(f"Successfully created temporary target user table: {self.temp_target_table}", 'info')

    def get_upgrade_level(self, level_cd):
        """获取可升级到指定等级的源等级列表的字符串 （升级可配置多个，降级只能配置一个）

        Args:
            level_cd (str): 目标等级

        Returns:
            str: 可以升级到目标等级的源等级列表，格式如 "'01', '02'"，如果没有则返回 None
        """
        # 查找所有可以升级到目标等级的源等级
        source_levels = [
            level['grdCd']
            for level in self.model_dict['levels']
            if level.get('upGrdCd') and level_cd in level['upGrdCd'].split(',')
        ]

        if source_levels:
            result_str = ', '.join([f"'{grd}'" for grd in source_levels])
            log_message(f"Source level list for level {level_cd}: {result_str}", 'info')
            return result_str
        else:
            log_message(f"No source level list found for level {level_cd}", 'info')
            return None

    def get_downgrade_level(self, level_cd):
        """通过降级等级编号获取降级等级配置信息

        Args:
            level_cd (str): 降级等级编号

        Returns:
            dict: 降级等级配置信息
        """
        for level in self.model_dict['levels']:
            if level['grdCd'] == level_cd:
                return level

    def get_non_new_user_level(self):
        """获取非新用户等级列表字符串

        Returns:
            str: 非新用户等级列表字符串，格式如 "'01', '02'"
        """
        target_levels = [
            level['grdCd']
            for level in self.model_dict['levels']
            if level.get('isGnu') and level['isGnu'] != '1'
        ]
        result_str = ', '.join([f"'{grd}'" for grd in target_levels])
        log_message(f"Other level list: {result_str}", 'info')
        return result_str

    def get_target_user(self, level_code, lvl_str, model_obj_cl):
        """根据等级获取目标用户的SQL
        生命周期：用户类型为 01-升级 02-保级延期/保级不延期/降级
        AARRR：用户类型暂时无用，不需要判断

        Args:
            level_code (str): 等级代码
            lvl_str (str): 可升级到该等级的来源等级代码字符串-lfcy / 非获客等级的用户等级代码字符串-aarrr （用','分割）
            model_obj_cl: 模型对象分类 01-个人用户 02-企业用户 03-企网通用户 04-聚合收单商户 05-本地生活商户

        Returns:
            tuple: 目标用户SQL列表
        """
        # 新用户-lfcy的获取新用户
        new_user_sql = f"""
        INSERT INTO {self.temp_target_user_table}
        SELECT a.usr_id AS obj_id,
            '{level_code}' AS level_code,
            '01' AS obj_tp,
            null AS valid_date
        FROM {self.obj_grp_tab} a   -- 根据对象类型，使用_grp表
        LEFT JOIN {self.target_table} b
            ON b.statt_dt = '{self.yds}'        -- 昨日结果表
            AND b.model_id = '{self.model_id}'
            AND a.usr_id = b.obj_id
        WHERE a.statt_dt = '{self.ds}'
        AND b.obj_id IS NULL                  -- 新增用户
        """

        # 全量用户 - aarrr的获取新用户
        all_user_sql = f"""
        INSERT INTO {self.temp_target_user_table}
        SELECT a.usr_id AS obj_id,
            '{level_code}' AS level_code,
            null AS obj_tp,
            null AS valid_date
        FROM {self.obj_grp_tab} a   -- 根据对象类型，使用_grp表
        WHERE a.statt_dt = '{self.ds}'
        """

        # 当日临时表中可达到该等级的用户-lfcy / 当日临时表中非获客阶段的用户-aarrr
        curr_to_level_sql = f"""
        INSERT INTO {self.temp_target_user_table}
        SELECT a.obj_id AS obj_id,
            '{level_code}' AS level_code,
            '01' AS obj_tp,
            null AS valid_date
        FROM {self.temp_target_table} a
        WHERE a.level_code IN ({lvl_str})
        """

        # 昨日目标表中可达到该等级的用户-lfcy / 昨日目标表中非获客阶段的用户-aarrr
        yst_to_level_sql = f"""
        INSERT INTO {self.temp_target_user_table}
        SELECT a.obj_id AS obj_id,
            '{level_code}' AS level_code,
            '01' AS obj_tp,
            null AS valid_date
        FROM {self.target_table} a
        WHERE a.statt_dt = '{self.yds}'     -- 昨日结果表
        AND a.model_id = '{self.model_id}'
        AND a.lvl_cd IN ({lvl_str})
        """

        # 昨日目标表中[该等级]的用户-lfcy
        yst_level_sql = f"""
        INSERT INTO {self.temp_target_user_table}
        SELECT a.obj_id AS obj_id,
            '{level_code}' AS level_code,
            '02' AS obj_tp,
            a.valid_matr_dt as valid_date  -- 无需类型转化
        FROM {self.target_table} a
        WHERE a.statt_dt = '{self.yds}'     -- 昨日结果表
        AND a.model_id = '{self.model_id}'
        AND a.lvl_cd = '{level_code}'
        """

        # 昨日+当日目标表中[该模型]的所有用户-aarrr
        # 增加当日目标表中的全量用户，否则只有第二天才会计算到其他等级
        model_all_user_sql = f"""
        INSERT INTO {self.temp_target_user_table}
        SELECT a.obj_id AS obj_id,
            '{level_code}' AS level_code,
            null AS obj_tp,
            null AS valid_date
        FROM {self.target_table} a
        WHERE a.statt_dt = '{self.yds}'     -- 昨日结果表
        AND a.model_id = '{self.model_id}'
        UNION ALL
        SELECT a.obj_id AS obj_id,
            '{level_code}' AS level_code,
            null AS obj_tp,
            null AS valid_date
        FROM {self.temp_target_table} a     -- 当日结果表
        """

        return new_user_sql, all_user_sql, curr_to_level_sql, yst_to_level_sql, yst_level_sql, model_all_user_sql

    def get_start_dt(self):
        """获取有效开始日期
        1. 回溯任务：取回溯日期
        2. 普通任务：
            - 未回溯过，取修改日期 (假如通过手动重跑，会出现有效开始日期为模型修改日期，而非重跑日期，通过回溯进行重跑则无问题)
            - 回溯过（已回溯完成），若完成后未修改过，取最近任务完成的回溯日期，若完成后修改过，取修改日期

        Returns:
            str: 有效开始日期
        """
        model_manager = ModelManager(self.model_type, self.model_id, self.rule_table, self.src_model_id_col)

        if self.is_trace == '1':
            # 获取回溯中或终止中任务的回溯日期，只会有这两种状态
            _, _, trace_dt, _ = model_manager.get_trace_info(status='02,04')
            return trace_dt
        else:
            # 普通任务
            # 获取最近回溯完成的任务的回溯日期
            _, _, trace_dt, trace_end_dt = model_manager.get_trace_info(status='03')
            # 获取修改日期
            modify_dt = model_manager.get_modify_dt()

            if trace_end_dt:
                # 回溯过，判断回溯结束日期是否大于修改日期，且小于跑批日期 -- 即回溯结束后再跑之后的取回溯日期，否则取修改日期
                # 回溯结束后，又单独跑了回溯之前的日期，则取修改日期
                return trace_dt if trace_end_dt > modify_dt and trace_end_dt < self.ds else modify_dt
            else:
                # 未回溯过，取修改日期
                return modify_dt

    @abstractmethod
    def evaluator(self):
        """规则判断

        子类需要实现此方法来处理具体的规则判断逻辑。
        """
        pass

    @abstractmethod
    def write_target_table(self):
        """将临时目标表写入目标表

        子类需要实现此方法来处理具体的写入目标表逻辑。
        """
        pass


class LFCYEvaluator(RuleEvaluator):
    """生命周期模型规则判断"""

    def evaluator(self):
        """规则判断：满足则按升级去向升级或保级延期，不满足且达到有效周期数，则按降级去向降级

        满足规则：先获取各等级的目标用户（涉及当日计算结果），再判断后更新目标临时表
        不满足规则：排除掉满足的当前该等级用户，判断有效期后更新目标临时表

        Raises:
            ValueError: 如果某个等级有多个条件组，则抛出异常。
            SystemExit: 如果执行SQL语句失败，则退出程序。
        """
        try:
            # 确保按照等级顺序处理
            levels_sorted = sorted(self.model_dict['levels'], key=lambda x: x.get('pl', 0))

            for level in levels_sorted:
                level_code = level['grdCd']
                period_num = level['periodNum']
                period_tp = level['periodTp']
                period_val = level['periodVal']
                # 20250324 等级中增加对象类型
                model_obj_cl = level['modelObjCl']

                is_gnu = level.get('isGnu', '2')  # 1-新用户，2-非新用户
                condition_groups = level.get('conditionGroups', [])

                # 判断规则情况：1. 只能有一个条件组，2. 没有条件组
                if len(condition_groups) > 1:
                    raise ValueError(f"Level {level_code} has multiple condition groups, but only one is allowed.", 'error')
                elif len(condition_groups) == 0:
                    log_message(f"Level {level_code} has no condition groups, skipping evaluation.", 'info')
                    # 没有is_uc_flg配置，且未配置规则，则为都不满足，直接跳过
                    log_message(f"Level {level_code} are not meet, skipping.", 'info')
                    continue

                cdt_desc = condition_groups[0]['cdtDesc']

                # 可升级到当前等级的源等级列表
                src_lvl_list = self.get_upgrade_level(level_code)

                new_user_sql, _, curr_to_level_sql, yst_to_level_sql, yst_level_sql, _ = self.get_target_user(level_code, src_lvl_list, model_obj_cl)

                # 计算等级有效日期
                _, valid_dt = get_date_range(self.ds, period_tp, period_val, period_num)

                # 1. 获取目标用户：按等级计算优化级依次获取该等级目标用户
                if is_gnu == '1':
                    # 新增用户
                    self.odpsex.execute_ddl_dml(new_user_sql)
                else:
                    # 非新增用户：升级（当日可升级等级+昨日可升级等级）+保级延期（昨日该等级）
                    if src_lvl_list:
                        # 当日可升到该等级的用户
                        self.odpsex.execute_ddl_dml(curr_to_level_sql)
                        # 昨日可升到该等级的用户
                        self.odpsex.execute_ddl_dml(yst_to_level_sql)

                    # 昨日该等级的用户（一定存在）
                    self.odpsex.execute_ddl_dml(yst_level_sql)

                # 2. 处理满足判断规则：要么升级，要么保级延期 -- merge到目标表
                # 按等级优先级将所有满足规则的用户更新完成
                # 01-升级 02-保级延期
                merge_sql = f"""
                MERGE INTO {self.temp_target_table} t
                USING (
                    SELECT distinct a.obj_id, a.obj_tp, a.level_code
                    FROM {self.temp_target_user_table} a
                    INNER JOIN (
                        SELECT obj_id
                        FROM {self.temp_wide_table}
                        WHERE {cdt_desc}
                    ) b ON a.obj_id = b.obj_id
                    WHERE a.obj_tp IN ('01', '02')
                    AND a.level_code = '{level_code}'
                ) u
                ON t.obj_id = u.obj_id
                WHEN MATCHED THEN
                    UPDATE SET t.level_code = u.level_code, t.chg_tp = u.obj_tp, t.valid_date = '{valid_dt}'
                WHEN NOT MATCHED THEN
                    INSERT VALUES (u.obj_id, u.level_code, u.obj_tp, '{valid_dt}', null, null, null, null, '{self.ds}')
                """
                self.odpsex.execute_ddl_dml(merge_sql)

                log_message(f"MEET: Successfully processed rules for level {level_code}.", 'info')

            log_message("All levels meet rules processed.", 'info')

            # 3. 处理不满足判断规则：要么降级，要么保级不延期 -- merge到目标表
            # 再单独更新不满足规则的所有等级的剩下用户 -- 昨日该等级（obj_tp = '02'） -- 排除满足的
            # 03-保级不延期 04-降级 （可直接关联判断更新）
            for level in levels_sorted:
                level_code = level['grdCd']
                down_grd_cd = level['downGrdCd']

                if down_grd_cd:
                    # 获取降级等级的配置，计算有效期
                    down_level = self.get_downgrade_level(level_code)
                    period_num = down_level['periodNum']
                    period_tp = down_level['periodTp']
                    period_val = down_level['periodVal']

                    to_level_cd = down_grd_cd
                    _, to_valid_dt = get_date_range(self.ds, period_tp, period_val, period_num)
                    to_valid_dt = f"'{to_valid_dt}'"
                else:
                    # 未配置降级等级，则保持当前等级，有效日期为原有效期
                    to_level_cd = level_code
                    to_valid_dt = 'null'

                merge_sql = f"""
                MERGE INTO {self.temp_target_table} t
                USING (
                    SELECT distinct a.obj_id,
                            CASE WHEN a.valid_date >= '{self.ds}' THEN '03'                     -- 保级不延期
                                WHEN a.valid_date < '{self.ds}' THEN '04' END AS chg_tp,        -- 降级
                            CASE WHEN a.valid_date >= '{self.ds}' THEN a.valid_date
                                WHEN a.valid_date < '{self.ds}' THEN coalesce({to_valid_dt}, a.valid_date) END AS valid_date,     -- 若未配置降级，则默认为原有效期
                            CASE WHEN a.valid_date >= '{self.ds}' THEN a.level_code
                                WHEN a.valid_date < '{self.ds}' THEN {to_level_cd} END AS level_code
                    FROM {self.temp_target_user_table} a
                    LEFT JOIN {self.temp_target_table} b ON a.obj_id = b.obj_id AND a.level_code = b.level_code
                    WHERE a.obj_tp = '02'
                    AND a.level_code = '{level_code}'
                    AND b.obj_id IS NULL                   -- 排除满足的, 只更新不满足的
                ) u
                ON t.obj_id = u.obj_id
                WHEN MATCHED THEN
                    UPDATE SET t.level_code = u.level_code, t.chg_tp = u.chg_tp, t.valid_date = u.valid_date
                WHEN NOT MATCHED THEN
                    INSERT VALUES (u.obj_id, u.level_code, u.chg_tp, u.valid_date, null, null, null, null, '{self.ds}')
                """
                self.odpsex.execute_ddl_dml(merge_sql)

                log_message(f"NOT MEET: Successfully processed rules for level {level_code}.", 'info')

            log_message("All levels not meet rules processed.", 'info')
        except Exception as e:
            raise

    def write_target_table(self):
        """将临时目标表写入目标表

        该方法将临时目标表中的数据写入最终的目标表中，并处理数据的有效日期。

        Raises:
            SystemExit: 如果执行SQL语句失败，则退出程序。
        """
        log_message("Start writing target table", 'info')
        # 删除目标分区 （按日期+模型分区）
        drop_sql = f"ALTER TABLE {self.target_table} DROP IF EXISTS PARTITION (statt_dt='{self.ds}', model_id='{self.model_id}')"

        # 获取有效开始日期
        log_message("Getting data valid start date", 'info')
        start_dt = self.get_start_dt()
        log_message(f"Data valid start date: {start_dt}", 'info')

        # 增加life_end_dt: 昨天为高危期及之后，今天也为高危期及之后，则不变，否则更新为最新日期
        insert_sql = f"""
            INSERT INTO {self.target_table} PARTITION (statt_dt='{self.ds}', model_id='{self.model_id}')
            SELECT
                '{self.model_obj_cl}' as model_obj_cl
                ,'{self.model_dict['modelNm']}' as model_nm
                ,a.obj_id as obj_id
                ,a.level_code as lvl_cd
                ,d.grd_nm as lvl_dsc
                ,case when a.level_code != b.lvl_cd then '{self.ds}' else null end as chg_dt
                ,a.chg_tp as chg_tp_cd
                ,case when a.chg_tp = '01' then '升级'
                    when a.chg_tp = '02' then '保级延期'
                    when a.chg_tp = '03' then '保级不延期'
                    when a.chg_tp = '04' then '降级' end as chg_tp_dsc
                ,b.lvl_cd as before_lvl_cd
                ,b.lvl_dsc as before_lvl_dsc
                ,a.valid_date as valid_matr_dt
                ,case when a.level_code in ('06', '07') and b.lvl_cd in ('06', '07') then b.life_end_dt else '{self.ds}' end as life_end_dt
                ,null as rmrk
                ,'{start_dt}' as start_dt        -- 有效开始日期：写入时规则判断后的日期
                ,'99991231' as end_dt            -- 有效结束日期：默认99991231，在最外层更新
                ,'lfcy_model.py' as etl_job
                ,null as etl_src_tab
            FROM {self.temp_target_table} a  -- 今日结果
            LEFT JOIN {self.target_table} b  -- 昨日结果
            ON a.obj_id = b.obj_id AND b.model_id = '{self.model_id}' AND b.statt_dt = '{self.yds}'
            LEFT JOIN {lfcy_tables['rule_grade']} d    -- 模型等级表
            ON a.level_code = d.grd_cd AND d.biz_id = '{self.model_id}' AND d.etl_dt = '{self.rule_ds}'
        """
        log_message("Dropping existing partition in target table if it exists...", 'info')
        self.odpsex.execute_ddl_dml(drop_sql)

        log_message("Inserting data into target table...", 'info')
        self.odpsex.execute_ddl_dml(insert_sql)
        log_message(f"Successfully wrote data to target table: {self.target_table}", 'info')


class AARRREvaluator(RuleEvaluator):
    """AARRR模型规则判断"""

    def evaluator(self):
        """规则判断

        该方法根据模型配置中的等级规则对用户进行评估，并将结果存储在临时目标表中。

        Raises:
            ValueError: 如果某个等级有多个条件组，则抛出异常。
            SystemExit: 如果执行SQL语句失败，则退出程序。
        """
        try:
            # 确保按照等级顺序处理
            levels_sorted = sorted(self.model_dict['levels'], key=lambda x: x.get('pl', 0))

            for level in levels_sorted:
                level_code = level['grdCd']
                is_gnu = level.get('isGnu', '2')  # 1-新用户，2-非新用户
                is_uc_flg = level.get('isUcFlg', '0')  # 1-无条件满足，0-条件满足
                condition_groups = level.get('conditionGroups', [])
                # 20250324 等级中增加对象类型
                model_obj_cl = level['modelObjCl']

                # 判断规则情况：1. 只能有一个条件组，2. 没有条件组
                if len(condition_groups) > 1:
                    raise ValueError(f"Level {level_code} has multiple condition groups, but only one is allowed.", 'error')
                elif len(condition_groups) == 0 and is_uc_flg != '1':
                    # 没有条件组，且没有无条件满足配置，则跳过
                    log_message(f"Level {level_code} has no condition groups, skipping evaluation.", 'info')
                    continue
                elif len(condition_groups) == 1:
                    log_message(f"Level {level_code} has one condition group, evaluating...", 'info')
                    cdt_desc = condition_groups[0]['cdtDesc']

                # 获取非新用户等级列表字符串
                non_new_user_level_str = self.get_non_new_user_level()

                _, all_user_sql, curr_to_level_sql, yst_to_level_sql, _, model_all_user_sql = self.get_target_user(level_code, non_new_user_level_str, model_obj_cl)

                # 1. 获取目标用户：按等级计算优化级依次获取该等级目标用户
                if is_gnu == '1':
                    # aarrr获取阶段的目标用户为全量用户，再根据条件筛选出符合条件的获取用户
                    self.odpsex.execute_ddl_dml(all_user_sql)
                elif is_uc_flg == '1' and non_new_user_level_str:
                    # 无条件满足：参与计算+无规则配置 （非获客等级的所有用户，作为兜底目标用户）
                    self.odpsex.execute_ddl_dml(curr_to_level_sql)
                    self.odpsex.execute_ddl_dml(yst_to_level_sql)
                else:
                    # 该模型所有用户 (昨日+当日)：参与等级变迁
                    self.odpsex.execute_ddl_dml(model_all_user_sql)

                # 2. 处理规则判断：无条件满足-兜底等级、条件判断满足
                if is_uc_flg == '1':
                    log_message(f"Level {level_code} has no condition groups, but isUcFlg is 1, evaluating...", 'info')
                    # 无条件满足：参与计算+无规则配置 （兜底等级放在最后面）
                    merge_query = f"""
                    MERGE INTO {self.temp_target_table} t
                    USING (
                    SELECT distinct a.obj_id
                    FROM {self.temp_target_user_table} a
                    LEFT JOIN {self.temp_target_table} b         -- 排除已经计算过的用户
                    ON a.obj_id = b.obj_id
                    WHERE a.level_code = '{level_code}'
                    AND b.obj_id IS NULL
                    ) u
                    ON t.obj_id = u.obj_id
                    WHEN MATCHED THEN
                        UPDATE SET t.level_code = '{level_code}'
                    WHEN NOT MATCHED THEN
                        INSERT VALUES (u.obj_id, '{level_code}', null, null, null, null, null, null, '{self.ds}')
                    """
                else:
                    # 条件判断满足
                    merge_query = f"""
                    MERGE INTO {self.temp_target_table} t
                    USING (
                        SELECT distinct a.obj_id
                        FROM {self.temp_target_user_table} a
                        INNER JOIN (
                            SELECT obj_id
                            FROM {self.temp_wide_table}
                            WHERE {cdt_desc}
                        ) b ON a.obj_id = b.obj_id
                        WHERE a.level_code = '{level_code}'
                    ) u
                    ON t.obj_id = u.obj_id
                    WHEN MATCHED THEN
                        UPDATE SET t.level_code = '{level_code}'
                    WHEN NOT MATCHED THEN
                        INSERT VALUES (u.obj_id, '{level_code}', null, null, null, null, null, null, '{self.ds}')
                    """
                self.odpsex.execute_ddl_dml(merge_query)
                log_message(f"Successfully evaluated level {level_code} and updated user levels.", 'info')

            log_message("Evaluated rules and updated user levels using MERGE statement.", 'info')
        except Exception as e:
            raise

    def write_target_table(self):
        """将临时目标表写入目标表

        该方法将临时目标表中的数据写入最终的目标表中，并处理数据的有效日期。

        Raises:
            SystemExit: 如果执行SQL语句失败，则退出程序。
        """
        log_message("Start writing target table", 'info')
        # 删除目标分区 （按日期+模型分区）
        drop_sql = f"ALTER TABLE {self.target_table} DROP IF EXISTS PARTITION (statt_dt='{self.ds}', model_id='{self.model_id}')"

        # 获取有效开始日期
        log_message("Getting data valid start date", 'info')
        start_dt = self.get_start_dt()
        log_message(f"Data valid start date: {start_dt}", 'info')

        insert_sql = f"""
            INSERT INTO {self.target_table} PARTITION (statt_dt='{self.ds}', model_id='{self.model_id}')
            SELECT
                '{self.model_obj_cl}' as model_obj_cl
                ,'{self.model_dict['modelNm']}' as model_nm
                ,a.obj_id as obj_id
                ,a.level_code as lvl_cd
                ,d.grd_nm as lvl_dsc
                ,case when a.level_code != b.lvl_cd then '{self.ds}' else null end as chg_dt
                ,b.lvl_cd as before_lvl_cd
                ,b.lvl_dsc as before_lvl_dsc
                ,null as rmrk
                ,'{start_dt}' as start_dt        -- 有效开始日期：写入时规则判断后的日期
                ,'99991231' as end_dt            -- 有效结束日期：默认99991231，在最外层更新
                ,'aarrr_model.py' as etl_job
                ,null as etl_src_tab
            FROM {self.temp_target_table} a  -- 今日结果
            LEFT JOIN {self.target_table} b  -- 昨日结果
            ON a.obj_id = b.obj_id AND b.model_id = '{self.model_id}' AND b.statt_dt = '{self.yds}'
            LEFT JOIN {aarrr_tables['rule_grade']} d    -- 模型等级表
            ON a.level_code = d.grd_cd AND d.ar_cd = '{self.model_id}' AND d.etl_dt = '{self.rule_ds}'
        """
        log_message("Dropping existing partition in target table if it exists...", 'info')
        self.odpsex.execute_ddl_dml(drop_sql)

        log_message("Inserting data into target table...", 'info')
        self.odpsex.execute_ddl_dml(insert_sql)
        log_message(f"Successfully wrote data to target table: {self.target_table}", 'info')


class RFMEvaluator(RuleEvaluator):
    """RFM模型规则判断"""

    def evaluator(self):
        """规则判断

        该方法根据模型配置中的等级规则对用户进行评估，并将结果存储在临时目标表中。

        Raises:
            ValueError: 如果某个等级有多个条件组，则抛出异常。
            SystemExit: 如果执行SQL语句失败，则退出程序。
        """
        try:
            # 不需要排序，为了保持一致
            levels_sorted = self.model_dict['levels']

            for level in levels_sorted:
                level_code = level['grdCd']
                condition_groups = level.get('conditionGroups', [])
                # 20250324 等级中增加对象类型
                model_obj_cl = level['modelObjCl']

                # 判断规则情况：1. 只能有一个条件组，2. 没有条件组
                if len(condition_groups) > 1:
                    raise ValueError(f"Level {level_code} has multiple condition groups, but only one is allowed.", 'error')
                elif len(condition_groups) == 0:
                    # 没有条件组，且没有无条件满足配置，则跳过
                    log_message(f"Level {level_code} has no condition groups, skipping evaluation.", 'info')
                    continue
                elif len(condition_groups) == 1:
                    log_message(f"Level {level_code} has one condition group, evaluating...", 'info')
                    cdt_desc = condition_groups[0]['cdtDesc']

                _, all_user_sql, _, _, _, _ = self.get_target_user(level_code, '', model_obj_cl)

                # 1. 获取目标用户：全量用户
                self.odpsex.execute_ddl_dml(all_user_sql)

                # 2. 处理规则判断：满足为1，不满足为0
                merge_query = f"""
                MERGE INTO {self.temp_target_table} t
                USING (
                    SELECT distinct a.obj_id, a.level_code, case when b.obj_id is not null then '1' else '0' end as level_value
                    FROM {self.temp_target_user_table} a
                    LEFT JOIN (
                        SELECT obj_id
                        FROM {self.temp_wide_table}     -- 关联指标宽表
                        WHERE {cdt_desc}
                    ) b ON a.obj_id = b.obj_id
                    WHERE a.level_code = '{level_code}'
                ) u
                ON t.obj_id = u.obj_id AND t.level_code = u.level_code
                WHEN MATCHED THEN
                    UPDATE SET t.level_value = u.level_value
                WHEN NOT MATCHED THEN
                    INSERT VALUES (u.obj_id, u.level_code, null, null, u.level_value, null, null, null, '{self.ds}')
                """
                self.odpsex.execute_ddl_dml(merge_query)
                log_message(f"Successfully evaluated level {level_code} and updated user levels.", 'info')

            log_message("Evaluated rules and updated user levels using MERGE statement.", 'info')
        except Exception as e:
            raise

    def write_target_table(self):
        """将临时目标表写入目标表

        该方法将临时目标表中的数据写入最终的目标表中，并处理数据的有效日期。

        Raises:
            SystemExit: 如果执行SQL语句失败，则退出程序。
        """
        log_message("Start writing target table", 'info')
        # 删除目标分区 （按日期+模型分区）
        drop_sql = f"ALTER TABLE {self.target_table} DROP IF EXISTS PARTITION (statt_dt='{self.ds}', model_id='{self.model_id}')"

        # 获取有效开始日期
        log_message("Getting data valid start date", 'info')
        start_dt = self.get_start_dt()
        log_message(f"Data valid start date: {start_dt}", 'info')

        insert_sql = f"""
            INSERT INTO {self.target_table} PARTITION (statt_dt='{self.ds}', model_id='{self.model_id}')
            SELECT
                '{self.model_obj_cl}' as model_obj_cl
                ,'{self.model_dict['modelNm']}' as model_nm
                ,b.obj_id as obj_id
                ,b.r_flg_bit as r_flg_bit
                ,b.f_flg_bit as f_flg_bit
                ,b.m_flg_bit as m_flg_bit
                ,b.rfm_cd as rfm_cd
                ,case b.rfm_cd
                    when '000' then '低价值消费者'
                    when '001' then '潜在流失高价值消费者'
                    when '010' then '沉默消费者'
                    when '011' then '潜在沉默高价值消费者'
                    when '100' then '潜力消费者'
                    when '101' then '潜力高价值消费者'
                    when '110' then '忠诚消费者'
                    when '111' then '高价值消费者'
                end as rfm_nm
                ,null as rmrk
                ,'{start_dt}' as start_dt        -- 有效开始日期：写入时规则判断后的日期
                ,'99991231' as end_dt           -- 有效结束日期：默认99991231，在最外层更新
                ,'rfm_model.py' as etl_job
                ,null as etl_src_tab
            FROM (
                select obj_id, r_flg_bit, f_flg_bit, m_flg_bit, concat(r_flg_bit, f_flg_bit, m_flg_bit) as rfm_cd
                from (
                    select obj_id, max(case when level_code = '01' then level_value else null end) as r_flg_bit,
                            max(case when level_code = '02' then level_value else null end) as f_flg_bit,
                            max(case when level_code = '03' then level_value else null end) as m_flg_bit
                    from {self.temp_target_table}
                    group by obj_id
                ) a
            ) b
        """
        log_message("Dropping existing partition in target table if it exists...", 'info')
        self.odpsex.execute_ddl_dml(drop_sql)

        log_message("Inserting data into target table...", 'info')
        self.odpsex.execute_ddl_dml(insert_sql)
        log_message(f"Successfully wrote data to target table: {self.target_table}", 'info')


class LTVEvaluator(RuleEvaluator):
    """LTV模型规则处理"""

    def evaluator(self):
        """规则判断

        该方法根据模型配置中的等级规则对用户进行评估，并将结果存储在临时目标表中。

        Raises:
            ValueError: 如果某个等级有多个条件组，则抛出异常。
            SystemExit: 如果执行SQL语句失败，则退出程序。
        """
        try:
            # 不需要排序，为了保持一致
            levels_sorted = self.model_dict['levels']

            for level in levels_sorted:
                level_code = level['grdCd']
                condition_groups = level.get('conditionGroups', [])

                # 判断规则情况：1. 只能有一个条件组，2. 没有条件组
                if len(condition_groups) > 1:
                    raise ValueError(f"Level {level_code} has multiple condition groups, but only one is allowed.", 'error')
                elif len(condition_groups) == 0:
                    # 没有条件组，且没有无条件满足配置，则跳过
                    log_message(f"Level {level_code} has no condition groups, skipping evaluation.", 'info')
                    continue
                elif len(condition_groups) == 1:
                    log_message(f"Level {level_code} has one condition group, evaluating...", 'info')
                    cdt_desc = condition_groups[0]['cdtDesc']

                # 1. 获取目标用户：无

                # 2. 从宽表中根据前端组合后的表达式计算结果 （系数已在表达式中处理拼接）
                merge_query = f"""
                MERGE INTO {self.temp_target_table} t
                USING (
                SELECT '01' as level_code, {cdt_desc} as level_value, usr_num, tx_num, usr_lt
                FROM {self.temp_wide_table} a
                ) u
                ON t.level_code = u.level_code
                WHEN MATCHED THEN
                    UPDATE SET t.level_value = u.level_value, t.usr_num = u.usr_num, t.tx_num = u.tx_num, t.usr_lt = u.usr_lt, t.statt_dt = '{self.rule_ds}'
                WHEN NOT MATCHED THEN
                    INSERT VALUES (null, u.level_code, null, null, u.level_value, u.usr_num, u.tx_num, u.usr_lt, '{self.rule_ds}')
                """
                self.odpsex.execute_ddl_dml(merge_query)
                log_message(f"Successfully evaluated level {level_code} and updated user levels.", 'info')

            log_message("Evaluated rules and updated user levels using MERGE statement.", 'info')
        except Exception as e:
            raise

    def write_target_table(self):
        """将临时目标表写入目标表

        该方法将临时目标表中的数据写入最终的目标表中，并处理数据的有效日期。

        Raises:
            SystemExit: 如果执行SQL语句失败，则退出程序。
        """
        log_message("Start writing target table", 'info')
        # 删除目标分区 （按日期+模型分区）
        drop_sql = f"ALTER TABLE {self.target_table} DROP IF EXISTS PARTITION (statt_dt='{self.ds}', model_id='{self.model_id}')"

        # 获取有效开始日期
        log_message("Getting data valid start date", 'info')
        start_dt = self.get_start_dt()
        log_message(f"Data valid start date: {start_dt}", 'info')

        # 月中跑：gmv*2，月末跑：gmv*1 用于估算
        insert_sql = f"""
            INSERT INTO {self.target_table} PARTITION (statt_dt='{self.ds}', model_id='{self.model_id}')
            SELECT
                '{self.model_obj_cl}' as model_obj_cl
                ,'{self.model_dict['modelNm']}' as model_nm
                ,a.lc_cd as lfcy_tp_cd
                ,b.nm as lfcy_tp_dsc
                , c.usr_lt * (c.level_value/c.usr_num) * (case when substr('{self.ds}', 7, 2) = '15' then 2 else 1 end) as ltv_val     -- ltv = lt * gmv/usr_num * (2或1)
                , c.usr_lt as lt_val
                , c.level_value/c.usr_num * (case when substr('{self.ds}', 7, 2) = '15' then 2 else 1 end) as apru_val
                , c.level_value * (case when substr('{self.ds}', 7, 2) = '15' then 2 else 1 end) as gmv_val
                , c.tx_num as tx_num
                , c.usr_num as tx_usr_cnt
                , null as rmrk
                ,'{start_dt}' as start_dt        -- 有效开始日期：写入时规则判断后的日期
                ,'99991231' as end_dt            -- 有效结束日期：默认99991231，在最外层更新
                ,'ltv_model.py' as etl_job
                ,null as etl_src_tab
            FROM {ltv_tables['rule_definition']} a          -- ltv定义表
            LEFT JOIN {lfcy_tables['rule_definition']} b    -- 生命周期定义表
            ON a.lc_cd = b.biz_id AND a.etl_dt = b.etl_dt
            LEFT JOIN {self.temp_target_table} c
            ON c.statt_dt = a.etl_dt
            WHERE a.etl_dt = '{self.rule_ds}' and a.ltv_cd = '{self.model_id}'
        """
        log_message("Dropping existing partition in target table if it exists...", 'info')
        self.odpsex.execute_ddl_dml(drop_sql)

        log_message("Inserting data into target table...", 'info')
        self.odpsex.execute_ddl_dml(insert_sql)
        log_message(f"Successfully wrote data to target table: {self.target_table}", 'info')


class GMVEvaluator(RuleEvaluator):
    """GMV模型规则处理"""

    def evaluator(self):
        """规则判断

        该方法根据模型配置中的等级规则对用户进行评估，并将结果存储在临时目标表中。

        Raises:
            ValueError: 如果某个等级有多个条件组，则抛出异常。
            SystemExit: 如果执行SQL语句失败，则退出程序。
        """
        try:
            # 不需要排序，为了保持一致
            levels_sorted = self.model_dict['levels']

            for level in levels_sorted:
                level_code = level['grdCd']
                condition_groups = level.get('conditionGroups', [])

                # 判断规则情况：1. 只能有一个条件组，2. 没有条件组
                if len(condition_groups) > 1:
                    raise ValueError(f"Level {level_code} has multiple condition groups, but only one is allowed.", 'error')
                elif len(condition_groups) == 0:
                    # 没有条件组，且没有无条件满足配置，则跳过
                    log_message(f"Level {level_code} has no condition groups, skipping evaluation.", 'info')
                    continue
                elif len(condition_groups) == 1:
                    log_message(f"Level {level_code} has one condition group, evaluating...", 'info')
                    cdt_desc = condition_groups[0]['cdtDesc']

                # 1. 获取目标用户：无

                # 2. 从宽表中根据前端组合后的表达式计算结果 （系数已在表达式中处理拼接）
                merge_query = f"""
                MERGE INTO {self.temp_target_table} t
                USING (
                SELECT '01' as level_code, {cdt_desc} as level_value, usr_num, tx_num
                FROM {self.temp_wide_table} a
                ) u
                ON t.level_code = u.level_code
                WHEN MATCHED THEN
                    UPDATE SET t.level_value = u.level_value, t.usr_num = u.usr_num, t.tx_num = u.tx_num
                WHEN NOT MATCHED THEN
                    INSERT VALUES (null, u.level_code, null, null, u.level_value, u.usr_num, u.tx_num, null, '{self.ds}')
                """
                self.odpsex.execute_ddl_dml(merge_query)
                log_message(f"Successfully evaluated level {level_code} and updated user levels.", 'info')

            log_message("Evaluated rules and updated user levels using MERGE statement.", 'info')
        except Exception as e:
            raise

    def write_target_table(self):
        """将临时目标表写入目标表

        该方法将临时目标表中的数据写入最终的目标表中，并处理数据的有效日期。

        Raises:
            SystemExit: 如果执行SQL语句失败，则退出程序。
        """
        log_message("Start writing target table", 'info')
        # 删除目标分区 （按日期+模型分区）
        drop_sql = f"ALTER TABLE {self.target_table} DROP IF EXISTS PARTITION (statt_dt='{self.ds}', model_id='{self.model_id}')"

        # 获取有效开始日期
        log_message("Getting data valid start date", 'info')
        start_dt = self.get_start_dt()
        log_message(f"Data valid start date: {start_dt}", 'info')

        # 每天跑，计算相关值
        insert_sql = f"""
            INSERT INTO {self.target_table} PARTITION (statt_dt='{self.ds}', model_id='{self.model_id}')
            SELECT
                '{self.model_obj_cl}' as model_obj_cl
                ,'{self.model_dict['modelNm']}' as model_nm
                , c.level_value as gmv_val
                , c.tx_num as tx_num
                , c.usr_num as tx_usr_cnt
                , null as rmrk
                ,'{start_dt}' as start_dt        -- 有效开始日期：写入时规则判断后的日期
                ,'99991231' as end_dt            -- 有效结束日期：默认99991231，在最外层更新
                ,'gmv_model.py' as etl_job
                ,null as etl_src_tab
            FROM {self.temp_target_table} c
        """
        log_message("Dropping existing partition in target table if it exists...", 'info')
        self.odpsex.execute_ddl_dml(drop_sql)

        log_message("Inserting data into target table...", 'info')
        self.odpsex.execute_ddl_dml(insert_sql)
        log_message(f"Successfully wrote data to target table: {self.target_table}", 'info')
