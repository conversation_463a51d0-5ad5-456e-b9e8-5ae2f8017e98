'''
Author: <PERSON><PERSON><PERSON> He
Date: 2024-10-11 11:01:45
LastEditors: <EMAIL>
LastEditTime: 2025-08-05 10:09:59
FilePath: /opr_model_v2/scripts/rule_json.py
Description: 获取配置规则，生成json对象

各对象类，只负责获取数据（每个模型单独配置），生成json为单独函数

Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
'''
import json
import sys
import pandas as pd
from models import Model, Level, ConditionGroup, Condition
from utils import log_message, timer, ODPSExecutor
from const import *


class LFCYProcessor:
    """LFCY 模型规则类"""

    def __init__(self, odps, ds, model_id=None):
        """初始化 LFCYProcessor 类

        Args:
            odps (ODPS): ODPS 连接对象
            ds (str): 当前日期
            model_id (str, optional): 模型ID。默认为 None，表示处理所有模型。
        """
        self.odps = odps
        self.odpsex = ODPSExecutor(odps)
        self.ds = ds
        self.model_id = model_id

    @timer
    def get_data(self):
        """获取 LFCY 数据

        Returns:
            DataFrame: models_df 模型数据
            DataFrame: levels_df 等级数据
            dict: conditions_group 规则组数据
            dict: conditions 条件数据
        """
        # 1. 获取启用的模型列表
        if self.model_id:
            log_message(f"Specified model ID: {self.model_id}")
            sub_clause = f"AND biz_id = '{self.model_id}'"
        else:
            sub_clause = ""

        model_query = f"""
        SELECT biz_id AS model_id, nm AS model_nm, model_obj_cl, grp_id, null as lfcy_cd, null as lfcy_lab_id
        FROM {lfcy_tables['rule_definition']}
        WHERE stu = '01' AND etl_dt = '{self.ds}'
        {sub_clause}
        """
        models_df = self.odpsex.execute_select(model_query)

        # 如果没有，则正常退出
        if models_df.empty:
            log_message("No models found, exiting...", 'info')
            sys.exit(0)

        log_message(f"Fetched {len(models_df)} models from LFCY table", 'debug')

        # 2. 获取对应模型的等级列表，按计算优先级排序
        # 关联升降级规则，分别获取升级去向及降级去向
        levels_df = {}
        for _, row in models_df.iterrows():
            model_id = row['model_id']

            # 升级可以配置多个，降级只能配置一个
            level_query = f"""
            with uad_tbl as (
                SELECT grd_cd, uad_tp, wm_concat(',', to_grd_cd) as to_grd_cds
                FROM {lfcy_tables['rule_upgrade']}
                WHERE etl_dt = '{self.ds}' and biz_id = '{model_id}'
                GROUP BY grd_cd, uad_tp
            )
            SELECT a.grd_cd, a.grd_nm, a.pl, null as is_uc_flg, a.is_gnu, a.num as period_num
                , b.to_grd_cds as up_grd_cd, c.to_grd_cds as down_grd_cd, a.cyc_tp as period_tp, a.cyc_val as period_val
                , aa.model_obj_cl
            FROM {lfcy_tables['rule_grade']} a
            LEFT JOIN {lfcy_tables['rule_definition']} aa     -- 20240325 新增：获取对象类型
                ON aa.etl_dt = '{self.ds}' and a.biz_id = aa.biz_id
            LEFT JOIN uad_tbl b    -- 升级
                ON b.uad_tp = '01' and b.grd_cd = a.grd_cd
            LEFT JOIN uad_tbl c    -- 降级
                ON c.uad_tp = '02' and c.grd_cd = a.grd_cd
            WHERE a.biz_id = '{model_id}'
            AND a.etl_dt = '{self.ds}'
            ORDER BY a.pl
            """
            levels_df[model_id] = self.odpsex.execute_select(level_query)

        log_message(f"Fetched {len(levels_df)} levels from LFCY_GRD table", 'debug')

        # 3. 获取每个等级总的规则表达式 -- 父条件组编号为空 (只会存在一条)
        conditions_group = {}
        for model_id, levels in levels_df.items():
            for _, level_row in levels.iterrows():
                level_code = level_row['grd_cd']
                condition_group_query = f"""
                SELECT cdt_no, cdt_desc
                FROM {lfcy_tables['rule_condition']}
                WHERE biz_id = '{model_id}'
                AND grd_cd = '{level_code}'
                AND parnt_cdt_no IS NULL
                AND etl_dt = '{self.ds}'
                """
                conditions_group[(model_id, level_code)] = self.odpsex.execute_select(condition_group_query)

        log_message(f"Fetched {len(conditions_group)} condition groups from LFCY_CDT table", 'debug')

        # 4. 获取每个等级的具体指标规则 (关联指标表取对应表及字段映射)
        # 关联等级表，获取统计周期到每条规则；不存在的字段置空
        conditions = {}
        for (model_id, level_code), condition_group in conditions_group.items():
            condition_lab_query = f"""
            SELECT a.cdt_id, a.cdt_no, a.obj_tp, a.prod_tp, a.prod_obj_tp as obj_grp_tp, a.grp_no, a.grp_nm
                , null as flt_lab_no, null as flt_lab_nm, null as flt_clc, null as flt_clc_val
                , a.lab_no, a.lab_nm, a.clc, a.clc_val, b.cyc_val as period_val, b.cyc_tp as period_tp
                , m.crspd_tab_en_nm, m.crspd_fld_en_nm, m.indx_flg, m.rltnp_dime_fld
                , null as flt_crspd_fld_en_nm
                , m.lab_dgre_en_nm as lab_dgre_en_nm
                , null as flt_expression, null as coef_val
                , aa.model_obj_cl
                , null as chnl_no
                , null as lfcy_cd, null as lfcy_lab_id, null as lfcy_lab_id_tab, null as lfcy_lab_id_fld, null as lfcy_lab_id_rltnp_col
            FROM {lfcy_tables['rule_label']} a
            LEFT JOIN {lfcy_tables['rule_definition']} aa     -- 20240325 新增：获取对象类型
                ON aa.etl_dt = '{self.ds}' and a.biz_id = aa.biz_id
            LEFT JOIN {lfcy_tables['rule_grade']} b
                ON b.etl_dt = '{self.ds}' and b.grd_cd = a.grd_cd and b.biz_id = a.biz_id
            LEFT JOIN adm_opma_pub_lab_map_confi_tab m
                ON m.statt_dt = '{self.ds}' and m.lab_id = a.lab_no
            WHERE a.biz_id = '{model_id}'
            AND a.grd_cd = '{level_code}'
            AND a.etl_dt = '{self.ds}'
            """
            conditions[(model_id, level_code)] = self.odpsex.execute_select(condition_lab_query)

        log_message(f"Fetched {len(conditions)} conditions from LFCY_CDT_LAB table", 'debug')

        return models_df, levels_df, conditions_group, conditions


class AARRRProcessor:
    """AARRR 模型规则类"""

    def __init__(self, odps, ds, model_id=None):
        """初始化 AARRRProcessor 类

        Args:
            odps (ODPS): ODPS 连接对象
            ds (str): 当前日期
            model_id (str, optional): 模型ID。默认为 None，表示处理所有模型。
        """
        self.odps = odps
        self.odpsex = ODPSExecutor(odps)
        self.ds = ds
        self.model_id = model_id

    @timer
    def get_data(self):
        """获取 AARRR 数据

        Returns:
            DataFrame: models_df 模型数据
            DataFrame: levels_df 等级数据
            dict: conditions_group 规则组数据
            dict: conditions 条件数据
        """
        # 1. 获取启用的 AARRR 模型列表
        if self.model_id:
            log_message(f"Specified model ID: {self.model_id}")
            sub_clause = f"AND ar_cd = '{self.model_id}'"
        else:
            sub_clause = ""

        model_query = f"""
        SELECT ar_cd AS model_id, nm AS model_nm, model_obj_cl, null as grp_id, null as lfcy_cd, null as lfcy_lab_id
        FROM {aarrr_tables['rule_definition']}
        WHERE stu = '01' AND etl_dt = '{self.ds}'
        {sub_clause}
        """
        models_df = self.odpsex.execute_select(model_query)

        log_message(f"Fetched {len(models_df)} models from AARRR table", 'debug')

        # 2. 获取对应模型的等级列表，按计算优先级排序
        # 增加其他字段，置为null
        levels_df = {}
        for _, row in models_df.iterrows():
            model_id = row['model_id']

            level_query = f"""
            SELECT a.grd_cd, a.grd_nm, a.pl, a.is_uc_flg, a.is_gnu, null as period_num
                , null as up_grd_cd, null as down_grd_cd, null as period_tp, null as period_val
                , b.model_obj_cl
            FROM {aarrr_tables['rule_grade']} a
            LEFT JOIN {aarrr_tables['rule_definition']} b
                ON a.ar_cd = b.ar_cd AND b.etl_dt = '{self.ds}'
            WHERE a.ar_cd = '{model_id}'
                AND a.etl_dt = '{self.ds}'
                ORDER BY a.pl
            """
            levels_df[model_id] = self.odpsex.execute_select(level_query)

        log_message(f"Fetched {len(levels_df)} levels from AARRR_GRD table", 'debug')

        # 3. 获取每个等级总的规则表达式 -- 父条件组编号为空 (只会存在一条)
        conditions_group = {}
        for model_id, levels in levels_df.items():
            for _, level_row in levels.iterrows():
                level_code = level_row['grd_cd']
                condition_group_query = f"""
                SELECT cdt_no, cdt_desc
                FROM {aarrr_tables['rule_condition']}
                WHERE ar_cd = '{model_id}'
                AND grd_cd = '{level_code}'
                AND parnt_cdt_no IS NULL
                AND etl_dt = '{self.ds}'
                """
                conditions_group[(model_id, level_code)] = self.odpsex.execute_select(condition_group_query)

        log_message(f"Fetched {len(conditions_group)} condition groups from AARRR_CDT table", 'debug')

        # 4. 获取每个等级的具体指标规则 (关联指标表取对应表及字段映射)
        conditions = {}
        for (model_id, level_code), condition_group in conditions_group.items():
            condition_lab_query = f"""
            SELECT a.cdt_id, a.cdt_no, a.obj_tp, a.obj_tp_cl as prod_tp, a.prod_obj_tp as obj_grp_tp, a.grp_no, a.grp_nm
                , a.flt_lab_no, a.flt_lab_nm, a.flt_clc, a.flt_clc_val
                , a.lab_no, a.lab_nm, a.clc, a.clc_val, a.ccl_val as period_val, a.ccl_tp as period_tp
                , b.crspd_tab_en_nm, b.crspd_fld_en_nm, b.indx_flg, b.rltnp_dime_fld
                , c.crspd_fld_en_nm as flt_crspd_fld_en_nm
                , b.lab_dgre_en_nm as lab_dgre_en_nm
                , null as flt_expression, null as coef_val
                , aa.model_obj_cl
                , a.chnl_no
                , null as lfcy_cd, null as lfcy_lab_id, null as lfcy_lab_id_tab, null as lfcy_lab_id_fld, null as lfcy_lab_id_rltnp_col
            FROM {aarrr_tables['rule_label']} a
            LEFT JOIN {aarrr_tables['rule_definition']} aa         -- 新增：获取model_obj_cl
                ON aa.etl_dt = '{self.ds}' and a.ar_cd = aa.ar_cd
            LEFT JOIN adm_opma_pub_lab_map_confi_tab b
                ON b.statt_dt = '{self.ds}' and b.lab_id = a.lab_no
            LEFT JOIN adm_opma_pub_lab_map_confi_tab c
                ON c.statt_dt = '{self.ds}' and c.lab_id = a.flt_lab_no
            WHERE a.ar_cd = '{model_id}'
            AND a.grd_cd = '{level_code}'
            AND a.etl_dt = '{self.ds}'
            """
            conditions[(model_id, level_code)] = self.odpsex.execute_select(condition_lab_query)

        log_message(f"Fetched {len(conditions)} conditions from AARRR_CDT_LAB table", 'debug')

        return models_df, levels_df, conditions_group, conditions


class RFMProcessor:
    """RFM 模型规则类"""

    def __init__(self, odps, ds, model_id=None):
        """初始化 RFMProcessor 类

        Args:
            odps (ODPS): ODPS 连接对象
            ds (str): 当前日期
            model_id (str, optional): 模型ID。默认为 None，表示处理所有模型。
        """
        self.odps = odps
        self.odpsex = ODPSExecutor(odps)
        self.ds = ds
        self.model_id = model_id

    @timer
    def get_data(self):
        """获取 RFM 数据

        Returns:
            DataFrame: models_df 模型数据
            DataFrame: levels_df 等级数据
            dict: conditions_group 规则组数据
            dict: conditions 条件数据
        """
        # 1. 获取启用的模型列表
        if self.model_id:
            log_message(f"Specified model ID: {self.model_id}")
            sub_clause = f"AND rfm_cd = '{self.model_id}'"
        else:
            sub_clause = ""

        model_query = f"""
        SELECT rfm_cd AS model_id, nm AS model_nm, model_obj_cl, null as grp_id, null as lfcy_cd, null as lfcy_lab_id
        FROM {rfm_tables['rule_definition']}
        WHERE stu = '01' AND etl_dt = '{self.ds}'
        {sub_clause}
        """
        models_df = self.odpsex.execute_select(model_query)

        log_message(f"Fetched {len(models_df)} models from RFM table", 'debug')

        # 2. 等级固定为三个，分别计算
        # 其他无关字段，置为null
        levels_df = {}
        for _, row in models_df.iterrows():
            model_id = row['model_id']

            level_query = f"""
            SELECT '01' as grd_cd, 'R-进度' as grd_nm, null as pl, null as is_uc_flg, null as is_gnu, null as period_num
                , null as up_grd_cd, null as down_grd_cd, null as period_tp, null as period_val
                , model_obj_cl
            FROM {rfm_tables['rule_definition']}
            WHERE stu = '01' AND etl_dt = '{self.ds}' {sub_clause}
            union all
            SELECT '02' as grd_cd, 'F-频次' as grd_nm, null as pl, null as is_uc_flg, null as is_gnu, null as period_num
                , null as up_grd_cd, null as down_grd_cd, null as period_tp, null as period_val
                , model_obj_cl
            FROM {rfm_tables['rule_definition']}
            WHERE stu = '01' AND etl_dt = '{self.ds}' {sub_clause}
            union all
            SELECT '03' as grd_cd, 'M-金额' as grd_nm, null as pl, null as is_uc_flg, null as is_gnu, null as period_num
                , null as up_grd_cd, null as down_grd_cd, null as period_tp, null as period_val
                , model_obj_cl
            FROM {rfm_tables['rule_definition']}
            WHERE stu = '01' AND etl_dt = '{self.ds}' {sub_clause}
            """
            levels_df[model_id] = self.odpsex.execute_select(level_query)

        log_message(f"Fetched {len(levels_df)} levels from AARRR_GRD table", 'debug')

        # 3. 获取每个等级总的规则表达式 -- 父条件组编号为空 (只会存在一条)
        conditions_group = {}
        for model_id, levels in levels_df.items():
            for _, level_row in levels.iterrows():
                level_code = level_row['grd_cd']
                condition_group_query = f"""
                SELECT cdt_no, cdt_desc
                FROM {rfm_tables['rule_condition']}
                WHERE rfm_cd = '{model_id}'
                AND rule_tp = '{level_code}'
                AND parnt_cdt_no IS NULL
                AND etl_dt = '{self.ds}'
                """
                conditions_group[(model_id, level_code)] = self.odpsex.execute_select(condition_group_query)

        log_message(f"Fetched {len(conditions_group)} condition groups from RFM_CDT table", 'debug')

        # 4. 获取每个等级的具体指标规则 (关联指标表取对应表及字段映射)
        conditions = {}
        for (model_id, level_code), condition_group in conditions_group.items():
            # 前端拼接了flt_expression，但码值需要转换，所以单独拼接处理，当前只支持单个类型的组内关系，不支持复杂的组合过滤条件
            condition_lab_query = f"""
            with flt_tbl as (
                select rfm_cd, concat_ws(case when cdt_in_tp = '01' then ' AND ' else ' OR ' end, collect_list(expression)) as flt_expression
                from (
                    select a.rfm_cd, a.cdt_in_tp, concat(b.crspd_fld_en_nm, a.flt_clc, '\\'', a.flt_clc_val, '\\'') as expression
                    from {rfm_tables['rule_filter']} a
                    left join adm_opma_pub_lab_map_confi_tab b on b.statt_dt = '{self.ds}' and a.flt_lab_no = b.lab_id
                    where a.rfm_cd = '{model_id}'
                    and a.etl_dt = '{self.ds}'
                )
                group by rfm_cd, cdt_in_tp
            )
            SELECT c.cdt_id, c.cdt_no, a.trg_cd as obj_tp, a.prod_tp, a.obj_tp as obj_grp_tp, a.obj_cd as grp_no, a.obj_nm as grp_nm
                , null as flt_lab_no, null as flt_lab_nm, null as flt_clc, null as flt_clc_val
                , c.lab_no, c.lab_nm, c.clc, c.clc_val, c.ccl_val as period_val, c.ccl_tp as period_tp
                , e.crspd_tab_en_nm, e.crspd_fld_en_nm, e.indx_flg, e.rltnp_dime_fld
                , null as flt_crspd_fld_en_nm
                , e.lab_dgre_en_nm as lab_dgre_en_nm
                , g.flt_expression, null as coef_val
                , a.model_obj_cl
                , null as chnl_no
                , null as lfcy_cd, null as lfcy_lab_id, null as lfcy_lab_id_tab, null as lfcy_lab_id_fld, null as lfcy_lab_id_rltnp_col
            FROM {rfm_tables['rule_definition']} a
            LEFT JOIN {rfm_tables['rule_condition']} b on b.etl_dt = '{self.ds}' and b.rfm_cd = a.rfm_cd and b.rule_tp = '{level_code}'
            LEFT JOIN {rfm_tables['rule_label']} c on c.etl_dt = '{self.ds}' and c.rfm_cd = a.rfm_cd and c.cdt_no = b.cdt_no and c.rule_tp = '{level_code}'
            LEFT JOIN adm_opma_pub_lab_map_confi_tab e on e.statt_dt = '{self.ds}' and e.lab_id = c.lab_no
            LEFT JOIN flt_tbl g on g.rfm_cd = a.rfm_cd
            WHERE a.rfm_cd = '{model_id}'
            AND a.etl_dt = '{self.ds}'
            AND c.cdt_id IS NOT NULL
            """
            conditions[(model_id, level_code)] = self.odpsex.execute_select(condition_lab_query)

        log_message(f"Fetched {len(conditions)} conditions from AARRR_CDT_LAB table", 'debug')

        return models_df, levels_df, conditions_group, conditions


class LTVProcessor:
    """LTV 模型规则类"""

    def __init__(self, odps, ds, model_id=None):
        """初始化 LTVProcessor 类

        Args:
            odps (ODPS): ODPS 连接对象
            ds (str): 当前日期
            model_id (str, optional): 模型ID。默认为 None，表示处理所有模型。
        """
        self.odps = odps
        self.odpsex = ODPSExecutor(odps)
        self.ds = ds
        self.model_id = model_id

    @timer
    def get_data(self):
        """获取 LTV 数据

        Returns:
            DataFrame: models_df 模型数据
            DataFrame: levels_df 等级数据  -- 使用gmv替换
            dict: conditions_group 规则组数据
            dict: conditions 条件数据
        """
        # 1. 获取启用的模型列表
        if self.model_id:
            log_message(f"Specified model ID: {self.model_id}")
            sub_clause = f"AND ltv_cd = '{self.model_id}'"
        else:
            sub_clause = ""

        model_query = f"""
        SELECT ltv_cd AS model_id, nm AS model_nm, model_obj_cl as model_obj_cl, null as grp_id, lc_cd as lfcy_cd, lt_id as lfcy_lab_id
        FROM {ltv_tables['rule_definition']}
        WHERE stu = '01' AND etl_dt = '{self.ds}'
        {sub_clause}
        """
        models_df = self.odpsex.execute_select(model_query)

        log_message(f"Fetched {len(models_df)} models from LTV table", 'debug')

        # 2. 没有等级表，置为指定值
        levels_df = {}
        for _, row in models_df.iterrows():
            model_id = row['model_id']

            level_query = f"""
            SELECT '01' as grd_cd, 'LTV' as grd_nm, null as pl, null as is_uc_flg, null as is_gnu, null as period_num
                , null as up_grd_cd, null as down_grd_cd, null as period_tp, null as period_val
                , model_obj_cl as model_obj_cl
            FROM {ltv_tables['rule_definition']}
            WHERE stu = '01' AND etl_dt = '{self.ds}'
            AND ltv_cd = '{model_id}'
            """
            levels_df[model_id] = self.odpsex.execute_select(level_query)

        log_message(f"Fetched {len(levels_df)} levels from LTV_GRD table", 'debug')

        # 3. 无等级表达式，使用gmv的表达式，用于计算gmv
        conditions_group = {}
        for model_id, levels in levels_df.items():
            for _, level_row in levels.iterrows():
                level_code = level_row['grd_cd']
                condition_group_query = f"""
                SELECT gmv_no as cdt_no, gmv_desc as cdt_desc
                from {ltv_tables['rule_gmv']}
                where ltv_cd = '{model_id}'
                and etl_dt = '{self.ds}'
                """
                conditions_group[(model_id, level_code)] = self.odpsex.execute_select(condition_group_query)

        log_message(f"Fetched {len(conditions_group)} condition groups from GMV table", 'debug')

        # 4. gmv中各个指标的规则
        # 统计周期：本月，即月初到当前
        conditions = {}
        for (model_id, level_code), condition_group in conditions_group.items():
            condition_lab_query = f"""
            SELECT a.cdt_no as cdt_id, a.cdt_no as cdt_no, null as obj_tp, null as prod_tp, null as obj_grp_tp, null as grp_no, null as grp_nm
                , null as flt_lab_no, null as flt_lab_nm, null as flt_clc, null as flt_clc_val
                , a.cal_lab as lab_no, a.cal_nm as lab_nm, null as clc, null as clc_val, null as period_val, null as period_tp
                , e.crspd_tab_en_nm, e.crspd_fld_en_nm, e.indx_flg, e.rltnp_dime_fld
                , null as flt_crspd_fld_en_nm
                , e.lab_dgre_en_nm as lab_dgre_en_nm
                , a.cdt_desc as flt_expression, a.cal_cft as coef_val
                , b.model_obj_cl as model_obj_cl
                , null as chnl_no
                , b.lc_cd as lfcy_cd, b.lt_id as lfcy_lab_id
                , f.crspd_tab_en_nm as lfcy_lab_id_tab, f.crspd_fld_en_nm as lfcy_lab_id_fld, f.rltnp_dime_fld as lfcy_lab_id_rltnp_col
            FROM {ltv_tables['rule_condition']} a
            LEFT JOIN {ltv_tables['rule_definition']} b on b.etl_dt = '{self.ds}' and b.ltv_cd = a.ltv_cd
            LEFT JOIN adm_opma_pub_lab_map_confi_tab e on e.statt_dt = '{self.ds}' and e.lab_id = a.cal_lab
            LEFT JOIN adm_opma_pub_lab_map_confi_tab f on f.statt_dt = '{self.ds}' and f.lab_id = b.lt_id
            WHERE a.ltv_cd = '{model_id}'
            AND a.etl_dt = '{self.ds}'
            """
            conditions[(model_id, level_code)] = self.odpsex.execute_select(condition_lab_query)

        log_message(f"Fetched {len(conditions)} conditions from LTV_GMV_CDT table", 'debug')

        return models_df, levels_df, conditions_group, conditions


class GMVProcessor:
    """GMV 模型规则类"""

    def __init__(self, odps, ds, model_id=None):
        """初始化 GMVProcessor 类

        Args:
            odps (ODPS): ODPS 连接对象
            ds (str): 当前日期
            model_id (str, optional): 模型ID。默认为 None，表示处理所有模型。
        """
        self.odps = odps
        self.odpsex = ODPSExecutor(odps)
        self.ds = ds
        self.model_id = model_id

    @timer
    def get_data(self):
        """获取 LTV 数据

        Returns:
            DataFrame: models_df 模型数据
            DataFrame: levels_df 等级数据  -- 无，默认01
            dict: conditions_group 规则组数据
            dict: conditions 条件数据
        """
        # 1. 获取启用的模型列表
        if self.model_id:
            log_message(f"Specified model ID: {self.model_id}")
            sub_clause = f"AND gmv_no = '{self.model_id}'"
        else:
            sub_clause = ""

        model_query = f"""
        SELECT gmv_no AS model_id, nm AS model_nm, model_obj_cl as model_obj_cl, null as grp_id, null as lfcy_cd, null as lfcy_lab_id
        FROM {gmv_tables['rule_definition']}
        WHERE stu = '01' AND etl_dt = '{self.ds}' and ltv_cd is null
        {sub_clause}
        """
        models_df = self.odpsex.execute_select(model_query)

        log_message(f"Fetched {len(models_df)} models from LTV table", 'debug')

        # 2. 没有等级表，置为指定值
        levels_df = {}
        for _, row in models_df.iterrows():
            model_id = row['model_id']

            level_query = f"""
            SELECT '01' as grd_cd, 'GMV' as grd_nm, null as pl, null as is_uc_flg, null as is_gnu, null as period_num
                , null as up_grd_cd, null as down_grd_cd, null as period_tp, null as period_val
                , model_obj_cl as model_obj_cl
            FROM {gmv_tables['rule_definition']}
            WHERE stu = '01' AND etl_dt = '{self.ds}'
            AND gmv_no = '{model_id}' and ltv_cd is null
            """
            levels_df[model_id] = self.odpsex.execute_select(level_query)

        log_message(f"Fetched {len(levels_df)} levels from GMV_GRD table", 'debug')

        # 3. 无等级表达式，使用gmv的表达式，用于计算gmv
        conditions_group = {}
        for model_id, levels in levels_df.items():
            for _, level_row in levels.iterrows():
                level_code = level_row['grd_cd']
                condition_group_query = f"""
                SELECT gmv_no as cdt_no, gmv_desc as cdt_desc
                from {gmv_tables['rule_definition']}
                where gmv_no = '{model_id}'
                and etl_dt = '{self.ds}'
                and ltv_cd is null
                """
                conditions_group[(model_id, level_code)] = self.odpsex.execute_select(condition_group_query)

        log_message(f"Fetched {len(conditions_group)} condition groups from GMV table", 'debug')

        # 4. gmv中各个指标的规则
        # 统计周期：本月，即月初到当前
        conditions = {}
        for (model_id, level_code), condition_group in conditions_group.items():
            condition_lab_query = f"""
            SELECT a.cdt_no as cdt_id, a.cdt_no as cdt_no, null as obj_tp, null as prod_tp, null as obj_grp_tp, null as grp_no, null as grp_nm
                , null as flt_lab_no, null as flt_lab_nm, null as flt_clc, null as flt_clc_val
                , a.cal_lab as lab_no, a.cal_nm as lab_nm, null as clc, null as clc_val, b.cyc_val as period_val, b.cyc_tp as period_tp
                , e.crspd_tab_en_nm, e.crspd_fld_en_nm, e.indx_flg, e.rltnp_dime_fld
                , null as flt_crspd_fld_en_nm
                , e.lab_dgre_en_nm as lab_dgre_en_nm
                , a.cdt_desc as flt_expression, a.cal_cft as coef_val
                , b.model_obj_cl as model_obj_cl
                , null as chnl_no
                , null as lfcy_cd, null as lfcy_lab_id
                , null as lfcy_lab_id_tab, null as lfcy_lab_id_fld, null as lfcy_lab_id_rltnp_col
            FROM {gmv_tables['rule_condition']} a
            LEFT JOIN {gmv_tables['rule_definition']} b on b.etl_dt = '{self.ds}' and b.gmv_no = a.gmv_no
            LEFT JOIN adm_opma_pub_lab_map_confi_tab e on e.statt_dt = '{self.ds}' and e.lab_id = a.cal_lab
            WHERE a.gmv_no = '{model_id}'
            AND a.etl_dt = '{self.ds}'
            """
            conditions[(model_id, level_code)] = self.odpsex.execute_select(condition_lab_query)

        log_message(f"Fetched {len(conditions)} conditions from GMV_CDT table", 'debug')

        return models_df, levels_df, conditions_group, conditions

class CACProcessor:
    """CAC 处理器类

    该类用于处理 CAC 相关的逻辑。 -  暂不需要，由前端直接计算，无需配置规则计算
    """
    pass


# 单独拆出来
@timer
def generate_json(models_df, levels_df, conditions_group, conditions):
    """构建json对象

    Args:
        models_df (DataFrame): 模型数据
        levels_df (DataFrame): 等级数据
        conditions_group (dict): 规则组数据
        conditions (dict): 条件数据

    Returns:
        str: 格式化后的 JSON 字符串
    """
    aarrr_json = {
        "models": []
    }

    for _, model_row in models_df.iterrows():
        model_id = model_row['model_id']
        model_name = model_row['model_nm']
        model_obj_cl = model_row['model_obj_cl']
        grp_id = model_row['grp_id']
        lfcy_cd = model_row['lfcy_cd']
        lfcy_lab_id = model_row['lfcy_lab_id']
        log_message(f"Processing model: {model_id} - {model_name} - {model_obj_cl}")
        model = Model(model_id, model_name, model_obj_cl, grp_id, lfcy_cd, lfcy_lab_id)

        for _, level_row in levels_df[model_id].iterrows():
            level_code = level_row['grd_cd']
            level_name = level_row['grd_nm']
            is_unconditional = level_row['is_uc_flg']
            is_new_user = level_row['is_gnu']
            priority = level_row['pl']
            period_num = level_row['period_num']
            up_level_code = level_row['up_grd_cd']
            down_level_code = level_row['down_grd_cd']
            period_tp = level_row['period_tp']
            period_val = level_row['period_val']
            model_obj_cl = level_row['model_obj_cl']
            log_message(f"Processing level: {level_code} - {level_name}")
            level = Level(level_code, level_name, priority, is_unconditional, is_new_user, period_num, up_level_code, down_level_code, period_tp, period_val, model_obj_cl)

            condition_group = conditions_group.get((model_id, level_code))
            if not condition_group.empty:
                condition_group_desc = condition_group.iloc[0]['cdt_desc']
                log_message(f"Condition group description: {condition_group_desc}")
                condition_group = ConditionGroup(condition_group_desc)

                condition_rows = conditions.get((model_id, level_code))
                if not condition_rows.empty:
                    for _, condition_row in condition_rows.iterrows():
                        log_message(f"Processing condition: {condition_row['cdt_id']}")
                        condition = Condition(
                            condition_row['cdt_id'],
                            condition_row['cdt_no'],
                            condition_row['obj_tp'],
                            condition_row['prod_tp'],
                            condition_row['obj_grp_tp'],
                            condition_row['grp_no'],
                            condition_row['grp_nm'],
                            condition_row['period_val'],
                            condition_row['period_tp'],
                            condition_row['flt_lab_no'],
                            condition_row['flt_lab_nm'],
                            condition_row['flt_clc'],
                            condition_row['flt_clc_val'],
                            condition_row['lab_no'],
                            condition_row['lab_nm'],
                            condition_row['clc'],
                            condition_row['clc_val'],
                            condition_row['crspd_tab_en_nm'],
                            condition_row['crspd_fld_en_nm'],
                            condition_row['indx_flg'],
                            condition_row['rltnp_dime_fld'],
                            condition_row['flt_crspd_fld_en_nm'],
                            condition_row['lab_dgre_en_nm'],
                            condition_row['flt_expression'],
                            condition_row['coef_val'],
                            condition_row['model_obj_cl'],
                            condition_row['chnl_no'],
                            condition_row['lfcy_cd'],
                            condition_row['lfcy_lab_id'],
                            condition_row['lfcy_lab_id_tab'],
                            condition_row['lfcy_lab_id_fld'],
                            condition_row['lfcy_lab_id_rltnp_col']
                        )
                        condition_group.add_condition(condition)

                    level.add_condition_group(condition_group)

            model.add_level(level)

        aarrr_json["models"].append(model.to_dict())

        log_message(f"Finished processing model json: {model_id} - {model_name} - {model_obj_cl}")

    return json.dumps(aarrr_json, ensure_ascii=False, indent=4)
