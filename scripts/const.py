'''
Author: <PERSON><PERSON><PERSON> He
Date: 2024-11-20 11:32:26
LastEditors: <EMAIL>
LastEditTime: 2025-07-11 10:34:52
FilePath: /opr_model_v2/scripts/const.py
Description: 常量配置

Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
'''

# 数据库连接配置
mysql_config = {
    'host': 'obproxy-bs01.ops.topcloud.fdb.dev',
    'port': 3306,
    'user': 'oppl_dpl@ob_tent_uat1#fudian_bsdev',
    'password': 'oppl_dpl_uat11122',
    'database': 'ob_oppl_uat1'
}

# 维度表映射
# 模型对象分类 （二期会新增）
# uid/eid/pid/商户，目前只有个人用户uid，后续再根据码值新增
# 20250324: 新需求，模型定义时可以指定用户圈群（针对基础用户进行限定）
#           用户维度表：原表不变，但在筛选目标用户时，使用原表上加后缀_grp的表（不管有无圈群限定）
#               -- 若有圈群：_grp表数据为圈群筛选后的，无圈群：_grp表数据与原表一致
# 修改数据结构：指明每个维度表的主键字段，以便关联
dim_table_map = {
    "01": ["adm_opma_usero_clc_ind_lab_di", 'usr_id'],  # 个人用户 (原有)
    "02": ["adm_opma_usero_corp_eid_clc_ind_lab_di", "corp_eid"],   # 企业用户 (新增)
    "03": ["adm_opma_usero_corp_pid_clc_ind_lab_di", "corp_pid"],   # 企网通用户（新增）
    "04": ["adm_opma_usero_corp_agg_clc_ind_lab_di", "mercht_id"],   # 聚合收单商户 （新增）
    "05": ["adm_opma_usero_corp_llep_clc_ind_lab_di", "mercht_id"],  # 本地生活商户（新增）
}


# 所有圈群关系表，整合为一个视图来查询，见group_ddl.sql
group_view = "v_group_rltnp"

# 回溯任务表 - ob
trace_task_table = "t_model_trace_inf"


# 生命周期相关表
lfcy_tables = {
    "src_definition": "t_user_life_cycle_dfe",
    "src_model_id_col": "biz_id",
    "rule_definition": "t_user_life_cycle_dfe_v2",
    "rule_grade": "t_user_life_cycle_grd_v2",
    "rule_condition": "t_user_life_cycle_grd_cdt_v2",
    "rule_label": "t_user_life_cycle_grd_cdt_lab_v2",
    "rule_upgrade": "t_user_life_cycle_uad_v2",
    "rule_result": "adm_opma_model_lfcy_rslt_dd_v2"
}

# AARRR模型相关表
aarrr_tables = {
    "src_definition": "t_user_aa_rrr",
    "src_model_id_col": "ar_cd",
    "rule_definition": "t_user_aa_rrr_v2",
    "rule_grade": "t_user_aa_rrr_grd_v2",
    "rule_condition": "t_user_aa_rrr_cdt_v2",
    "rule_label": "t_user_aa_rrr_cdt_lab_v2",
    "rule_result": "adm_opma_model_aarrr_rslt_dd_v2"
}

# RFM模型相关表
rfm_tables = {
    "src_definition": "t_user_rfm",
    "src_model_id_col": "rfm_cd",
    "rule_definition": "t_user_rfm_v2",
    "rule_condition": "t_user_rfm_cdt_v2",
    "rule_label": "t_user_rfm_cdt_lab_v2",
    "rule_filter": "t_user_rfm_flt_cdt_v2",
    "rule_result": "adm_opma_model_rfm_rslt_dd_v2"
}

# LTV模型相关表 (包含GMV模型)
ltv_tables = {
    "src_definition": "t_user_ltv",
    "src_model_id_col": "ltv_cd",
    "rule_definition": "t_user_ltv_v2",
    "rule_gmv": "t_user_ltv_gmv_v2",
    "rule_gmv_grp": "t_user_ltv_gmv_grp_v2",
    "rule_condition": "t_user_ltv_gmv_cdt_v2",
    "rule_label": "t_user_ltv_gmv_cdt_lab_v2",
    "rule_result": "adm_opma_model_ltv_rslt_dd_v2"
}
