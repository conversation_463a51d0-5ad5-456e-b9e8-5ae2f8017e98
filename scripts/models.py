'''
Author: <PERSON><PERSON><PERSON> He
Date: 2024-10-11 10:56:04
LastEditors: <EMAIL>
LastEditTime: 2025-07-11 17:23:10
FilePath: /opr_model_v2/scripts/models.py
Description: 模型配置表的通用模型定义 （所有模型使用同一个，在此基础上扩展）

Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
'''

class Model:
    """模型规则定义"""

    def __init__(self, model_id, model_nm, model_obj_cl, grp_id, lfcy_cd=None, lfcy_lab_id=None):
        """初始化模型规则

        Args:
            model_id (str): 模型ID
            model_nm (str): 模型名称
            model_obj_cl (str): 模型对象分类
            grp_id (str): 圈群编号（非必须）
            lfcy_cd (str): 生命周期编码
            lfcy_lab_id (str): 新手期标签编号
        """
        self.model_id = model_id
        self.model_nm = model_nm
        self.model_obj_cl = model_obj_cl
        self.grp_id = grp_id
        # ltv 增加生命周期编码及新手期标签编号
        self.lfcy_cd = lfcy_cd
        self.lfcy_lab_id = lfcy_lab_id
        self.levels = []

    def add_level(self, level):
        """将等级规则添加到列表中

        Args:
            level (dict): 等级规则字典
        """
        self.levels.append(level)

    def to_dict(self):
        """将模型转换为字典

        Returns:
            dict: 模型的字典表示
        """
        return {
            "modelId": self.model_id,
            "modelNm": self.model_nm,
            "modelObjCl": self.model_obj_cl,
            "grpId": self.grp_id,
            "lfcyCd": self.lfcy_cd,
            "lfcyLabId": self.lfcy_lab_id,
            "levels": [level.to_dict() for level in self.levels]
        }


class Level:
    """等级规则定义"""

    def __init__(self, grd_cd, grd_nm, pl, is_uc_flg, is_gnu, period_num, up_grd_cd, down_grd_cd, period_tp, period_val, model_obj_cl):
        """初始化等级规则

        Args:
            grd_cd (str): 等级代码
            grd_nm (str): 等级名称
            pl (int): 优先级
            is_uc_flg (bool): 是否无条件满足标志
            is_gnu (bool): 是否新用户
            period_num (int, optional): 有效周期数
            up_grd_cd (str, optional): 升级去向等级代码
            down_grd_cd (str, optional): 降级去向等级代码
            period_tp (str, optional): 统计周期类型 dd/mm
            period_val (int, optional): 统计周期值
            model_obj_cl (str): 模型对象分类 (20250324新增)
        """
        self.grd_cd = grd_cd
        self.grd_nm = grd_nm
        self.pl = pl
        self.is_gnu = is_gnu

        # aarrr特有
        self.is_uc_flg = is_uc_flg
        # 前端展示更新，数据处理时使用其他两个，这个字段未用到
        # self.is_clc_flg = is_clc_flg

        # lfcy特有
        self.period_num = period_num
        self.up_grd_cd = up_grd_cd
        self.down_grd_cd = down_grd_cd
        self.period_tp = period_tp
        self.period_val = period_val

        # 新增字段：模型对象分类
        self.model_obj_cl = model_obj_cl

        self.condition_groups = []

    def add_condition_group(self, condition_group):
        """将条件组添加到列表中

        Args:
            condition_group (ConditionGroup): 条件组对象
        """
        self.condition_groups.append(condition_group)

    def to_dict(self):
        """将等级规则转换为字典

        Returns:
            dict: 等级规则的字典表示
        """
        return {
            "grdCd": self.grd_cd,
            "grdNm": self.grd_nm,
            "pl": self.pl,
            "isUcFlg": self.is_uc_flg,
            "isGnu": self.is_gnu,
            "periodNum": self.period_num,
            "upGrdCd": self.up_grd_cd,
            "downGrdCd": self.down_grd_cd,
            "periodTp": self.period_tp,
            "periodVal": self.period_val,
            "modelObjCl": self.model_obj_cl,
            "conditionGroups": [group.to_dict() for group in self.condition_groups]
        }


class ConditionGroup:
    """条件组定义"""

    def __init__(self, cdt_desc):
        """初始化条件组

        Args:
            cdt_desc (str): 条件描述
        """
        self.cdt_desc = cdt_desc
        self.conditions = []

    def add_condition(self, condition):
        """将条件添加到列表中

        Args:
            condition (Condition): 条件对象
        """
        self.conditions.append(condition)

    def to_dict(self):
        """将条件组转换为字典

        Returns:
            dict: 条件组的字典表示
        """
        return {
            "cdtDesc": self.cdt_desc,
            "conditions": [cond.to_dict() for cond in self.conditions]
        }


class Condition:
    """指标条件定义"""

    def __init__(self, cdt_id, cdt_no, obj_tp, prod_tp, obj_grp_tp, grp_no,
                grp_nm, period_val, period_tp, flt_lab_no, flt_lab_nm,
                flt_clc, flt_clc_val, lab_no, lab_nm, clc, clc_val,
                crspd_tab_en_nm, crspd_fld_en_nm, indx_flg, rltnp_dime_fld,
                flt_crspd_fld_en_nm, lab_dgre_en_nm, flt_expression, coef_val, model_obj_cl, chnl_no,
                lfcy_cd, lfcy_lab_id, lfcy_lab_id_tab, lfcy_lab_id_fld, lfcy_lab_id_rltnp_col):
        """初始化条件

        Args:
            cdt_id (str): 条件ID
            cdt_no (str): 条件编号
            obj_tp (str): 对象大类：用户/产品/活动
            prod_tp (str): 产品大类：产品/积分/权益
            obj_grp_tp (str): 对象群分类：单个对象/圈群对象
            grp_no (str): 对象编号/圈群编号
            grp_nm (str): 对象名称/圈群名称
            period_val (str): 统计周期值
            period_tp (str): 统计周期类型 dd/mm
            flt_lab_no (str): 过滤标签编号
            flt_lab_nm (str): 过滤标签名称
            flt_clc (str): 过滤计算
            flt_clc_val (str): 过滤计算值
            lab_no (str): 标签编号
            lab_nm (str): 标签名称
            clc (str): 判断符
            clc_val (str): 判断值
            crspd_tab_en_nm (str): 指标对应物理表
            crspd_fld_en_nm (str): 指标对应物理字段
            indx_flg (str): 指标类型
            rltnp_dime_fld (str): 关联维度字段
            flt_crspd_fld_en_nm (str): 筛选条件对应物理字段
            lab_dgre_en_nm (str): 标签度量名称
            flt_expression (str): 筛选条件表达式
            coef_val (str): 系数值: gmv使用
            model_obj_cl (str): 模型对象类型 (20250324新增)
            chnl_no (str): 渠道编号（多选，逗号分隔， 20250430新增）
            lfcy_cd (str): 生命周期编码 (ltv)
            lfcy_lab_id (str): 新手期标签编号 (ltv)
            lfcy_lab_id_tab (str): 新手期标签对应物理表 (ltv)
            lfcy_lab_id_fld (str): 新手期标签对应物理字段 (ltv)
            lfcy_lab_id_rltnp_col (str): 新手期指标的关联字段 (ltv)
        """
        self.cdt_id = cdt_id
        self.cdt_no = cdt_no
        self.obj_tp = obj_tp
        self.prod_tp = prod_tp
        self.obj_grp_tp = obj_grp_tp
        self.grp_no = grp_no
        self.grp_nm = grp_nm
        self.period_val = period_val
        self.period_tp = period_tp

        # aarrr特有
        self.flt_lab_no = flt_lab_no
        self.flt_lab_nm = flt_lab_nm
        self.flt_clc = flt_clc
        self.flt_clc_val = flt_clc_val
        self.flt_crspd_fld_en_nm = flt_crspd_fld_en_nm  # 筛选条件对应物理字段


        self.lab_no = lab_no
        self.lab_nm = lab_nm
        self.clc = clc
        self.clc_val = clc_val
        self.crspd_tab_en_nm = crspd_tab_en_nm  # 指标对应物理表
        self.crspd_fld_en_nm = crspd_fld_en_nm  # 指标对应物理字段
        self.indx_flg = indx_flg                # 指标类型
        self.rltnp_dime_fld = rltnp_dime_fld    # 关联维度字段 （当事人编号字段）即主体对象
        self.lab_dgre_en_nm = lab_dgre_en_nm    # 标签度量名称
        self.flt_expression = flt_expression    # 筛选条件表达式

        # gmv特有
        self.coef_val = coef_val                  # 系数值

        # 20250324新增
        self.model_obj_cl = model_obj_cl
        # 20250430新增
        self.chnl_no = chnl_no
        # ltv 新增生命周期编码及新手期标签编号
        self.lfcy_cd = lfcy_cd
        self.lfcy_lab_id = lfcy_lab_id
        self.lfcy_lab_id_tab = lfcy_lab_id_tab
        self.lfcy_lab_id_fld = lfcy_lab_id_fld
        # 新手期指标的关联字段
        self.lfcy_lab_id_rltnp_col = lfcy_lab_id_rltnp_col

    def to_dict(self):
        """将条件转换为字典

        Returns:
            dict: 条件的字典表示
        """
        return {
            "cdtId": self.cdt_id,
            "cdtNo": self.cdt_no,
            "objTp": self.obj_tp,
            "prodTp": self.prod_tp,
            "objGrpTp": self.obj_grp_tp,
            "grpNo": self.grp_no,
            "grpNm": self.grp_nm,
            "periodVal": self.period_val,
            "periodTp": self.period_tp,
            "fltLabNo": self.flt_lab_no,
            "fltLabNm": self.flt_lab_nm,
            "fltClc": self.flt_clc,
            "fltClcVal": self.flt_clc_val,
            "fltCrspdFldEnNm": self.flt_crspd_fld_en_nm,
            "labNo": self.lab_no,
            "labNm": self.lab_nm,
            "clc": self.clc,
            "clcVal": self.clc_val,
            "crspdTabEnNm": self.crspd_tab_en_nm,
            "crspdFldEnNm": self.crspd_fld_en_nm,
            "indxFlg": self.indx_flg,
            "rltnpDimeFld": self.rltnp_dime_fld,
            "labDgreEnNm": self.lab_dgre_en_nm,
            "fltExpression": self.flt_expression,
            "coefVal": self.coef_val,
            "modelObjCl": self.model_obj_cl,
            "chnlNo": self.chnl_no,
            "lfcyCd": self.lfcy_cd,
            "lfcyLabId": self.lfcy_lab_id,
            "lfcyLabIdTab": self.lfcy_lab_id_tab,
            "lfcyLabIdFld": self.lfcy_lab_id_fld,
            "lfcyLabIdRltnpCol": self.lfcy_lab_id_rltnp_col
        }
