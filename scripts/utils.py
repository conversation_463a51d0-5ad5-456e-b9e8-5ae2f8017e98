'''
Author: <PERSON><PERSON><PERSON> He
Date: 2024-10-09 16:40:29
LastEditors: <EMAIL>
LastEditTime: 2025-07-18 17:58:33
FilePath: /opr_model_v2/scripts/utils.py
Description: 辅助函数和类

Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
'''
import json
import logging
import sys
import time
import pandas as pd
from functools import wraps
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import traceback

# 在调用脚本中引用三方包资源，不在此处引用，但仍需append
sys.path.append("pymysql.zip")
import pymysql

# 创建日志记录器
logger = logging.getLogger('opr_model')

# 设置日志级别 (DEBUG/INFO/WARNING/ERROR/CRITICAL)
LOG_LEVEL = logging.DEBUG
logger.setLevel(LOG_LEVEL)

# 禁止日志向上传递，消除日志重复
logger.propagate = False

# 创建日志格式
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 创建控制台处理器,使用相同的日志级别
ch = logging.StreamHandler()
ch.setLevel(LOG_LEVEL)
ch.setFormatter(formatter)

# 添加处理器到日志记录器
logger.addHandler(ch)

def log_message(message, level='info'):
    """
    根据提供的信息级别记录消息到日志。

    Args:
        message (str): 要记录的消息
        level (str, optional): 日志级别，默认为 'info'
    """
    if level == 'info':
        logger.info(message)
    elif level == 'debug':
        logger.debug(message)
    elif level == 'warning':
        logger.warning(message)
    elif level == 'error':
        logger.error(message)
        # 增加出错堆栈信息
        logger.error(traceback.format_exc())
    else:
        logger.critical(message)
        logger.critical(traceback.format_exc())


def timer(func):
    """
    装饰器，用于计算并打印函数执行所需的时间。

    Args:
        func (callable): 被装饰的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
        except Exception as e:
            log_message(f'Function {func.__name__} failed with error: {e}', 'error')
            raise
        end_time = time.time()
        execution_time = end_time - start_time
        log_message(f'Function {func.__name__} executed in {execution_time:.4f}s', 'info')
        return result
    return wrapper


class ODPSExecutor:
    """
    ODPS 执行器类，用于执行 SQL 语句。

    Attributes:
        odps (ODPS): ODPS 连接对象
    """

    def __init__(self, odps):
        """
        初始化 ODPSExecutor 类。

        Args:
            odps (ODPS): ODPS 连接对象
        """
        self.odps = odps

    @timer
    def execute_ddl_dml(self, sql_query):
        """
        执行 DDL/DML 语句（如 CREATE, INSERT 等）。

        Args:
            sql_query (str): 要执行的 SQL 语句
        """
        log_message(f"Executing ODPS SQL: {sql_query}", 'debug')
        try:
            instance = self.odps.execute_sql(sql_query)
            instance.wait_for_success()
        except Exception as e:
            log_message(f"Error executing SQL: {sql_query}\nError: {e}", 'error')
            raise

    @timer
    def execute_select(self, sql_query):
        """
        执行 SELECT 语句并返回结果作为 pandas DataFrame。

        Args:
            sql_query (str): 要执行的 SQL 语句

        Returns:
            pandas.DataFrame: 查询结果
        """
        log_message(f"Executing ODPS SQL: {sql_query}", 'debug')
        try:
            instance = self.odps.execute_sql(sql_query)
            instance.wait_for_success()
            result = instance.open_reader().to_pandas()
            return result
        except Exception as e:
            log_message(f"Error executing SQL: {sql_query}\nError: {e}", 'error')
            raise


def parse_json(json_str):
    """
    解析 JSON 配置文件，并将各个字段添加到属性中。

    Args:
        json_str (str): JSON 格式的配置字符串

    Returns:
        dict: 解析后的配置字典，如果解析失败则返回 None
    """
    try:
        config = json.loads(json_str)
        if len(config['models']) == 0:
            log_message("Empty JSON string", level="error")
            raise Exception("Empty JSON string")
        return config
    except json.JSONDecodeError as e:
        log_message(f"Invalid JSON format: {e}", level="error")
        raise Exception(f"Invalid JSON format: {e}")


def get_yesterday_date(date_str=None):
    """
    获取昨天的日期，格式为 YYYYMMDD。
    基准日期：若指定日期，则使用指定日期，不指定日期，则使用当前日期。

    Returns:
        str: 昨天的日期字符串
    """
    if date_str is None:
        curr_date = datetime.now()
    else:
        curr_date = datetime.strptime(date_str, '%Y%m%d')
    yesterday = curr_date - timedelta(days=1)
    return yesterday.strftime('%Y%m%d')


def get_date_range(date_str, period_tp, period_val, peroid_num=1):
    """通过规则配置，计算出统计周期开始日期，以及等级有效日期

    Args:
        date_str (str): 日期字符串
        period_tp (str): dd-天/mm-月
        period_val (int): 周期类型数值
        peroid_num (int, optional): 有效周期数量. Defaults to 1.

    Returns:
        tuple: 开始日期字符串，有效日期字符串
    """
    dt = datetime.strptime(date_str, '%Y%m%d')

    if not period_val or len(period_tp) !=2:
        start_dt = date_str
        valid_dt = '99991231'
    elif not peroid_num:
        valid_dt = '99991231'
    else:
        period_val = int(period_val)
        peroid_num = int(peroid_num)
        try:
            if period_tp == 'dd':
                start_dt = (dt - relativedelta(days=period_val - 1)).strftime('%Y%m%d')
                valid_dt = (dt + relativedelta(days=period_val * peroid_num - 1)).strftime('%Y%m%d')
            elif period_tp == 'mm':
                start_dt = (dt - relativedelta(months=period_val)).strftime('%Y%m%d')
                valid_dt = (dt + relativedelta(months=period_val * peroid_num)).strftime('%Y%m%d')
            else:
                raise ValueError(f"Invalid period_tp: {period_tp}", 'error')
        except Exception as e:
            log_message(f"Error in get_valid_date: {e}", 'error')
            raise

    return start_dt, valid_dt


def get_date_range_list(date_str, is_ltv=False):
    """
    根据指定日期返回截止到昨日的所有日期

    Args:
        date_str (str): 日期字符串
        is_ltv (bool): 是否为LTV模型

    Returns:
        list: 日期列表
    """
    yesterday = get_yesterday_date()

    if is_ltv:
        # ltv模型，根据回溯日期月份，只取月中（月初15号）和月末的日期
        month_start = pd.date_range(start=date_str, end=yesterday, freq='MS').strftime('%Y%m15').tolist()
        month_end = pd.date_range(start=date_str, end=yesterday, freq='M').strftime('%Y%m%d').tolist()
        date_range = sorted(month_start + month_end)
    else:
        date_range = pd.date_range(start=date_str, end=yesterday).strftime('%Y%m%d').tolist()
    return date_range


class MySQLHelper:
    """MySQL数据库操作类"""

    def __init__(self, host, port, user, password, database):
        """
        初始化数据库连接

        Args:
            host (str): 数据库主机地址
            port (int): 端口号
            user (str): 用户名
            password (str): 密码
            database (str): 数据库名
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.conn = None
        self.cursor = None

    def connect(self):
        """建立数据库连接"""
        try:
            self.conn = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database
            )
            self.cursor = self.conn.cursor()
        except Exception as e:
            log_message(f"Database connection failed: {e}", 'error')
            raise

    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()

    def execute_query(self, sql, params=None):
        """
        执行查询语句

        Args:
            sql (str): SQL查询语句
            params (tuple): 查询参数

        Returns:
            list: 查询结果列表
        """
        log_message(f"Executing Mysql SQL: {sql}", 'debug')
        try:
            self.connect()
            self.cursor.execute(sql, params)
            results = self.cursor.fetchall()
            return results
        except Exception as e:
            log_message(f"Database query execution failed: {e}", 'error')
            raise
        finally:
            self.close()

    def execute_update(self, sql, params=None):
        """
        执行更新语句

        Args:
            sql (str): SQL更新语句
            params (tuple): 更新参数

        Returns:
            int: 受影响的行数
        """
        log_message(f"Executing Mysql SQL: {sql}", 'debug')
        try:
            self.connect()
            rows = self.cursor.execute(sql, params)
            self.conn.commit()
            return rows
        except Exception as e:
            if self.conn:
                self.conn.rollback()
            log_message(f"Database update execution failed: {e}", 'error')
            raise
        finally:
            self.close()

    def execute_many(self, sql, params_list):
        """
        批量执行SQL语句

        Args:
            sql (str): SQL语句
            params_list (list): 参数列表

        Returns:
            int: 受影响的行数
        """
        log_message(f"Executing Mysql SQL: {sql}", 'debug')
        try:
            self.connect()
            rows = self.cursor.executemany(sql, params_list)
            self.conn.commit()
            return rows
        except Exception as e:
            if self.conn:
                self.conn.rollback()
            log_message(f"Database batch execution failed: {e}", 'error')
            raise
        finally:
            self.close()
