'''
Author: <PERSON><PERSON><PERSON> He
Date: 2024-10-21 17:59:06
LastEditors: <EMAIL>
LastEditTime: 2024-11-21 15:33:20
FilePath: /opr_model_v2/scripts/update_valid_dates.py
Description: 按模型更新结果表中有效历史数据的有效结束日期 （无需更新有效开始日期）

Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
'''
from utils import log_message, timer, ODPSExecutor, get_yesterday_date
from mysql_model_manager import ModelManager
from const import *


class UpdateValidDates:
    """
    更新结果表中有效历史数据的有效结束日期

    Attributes:
        odps (ODPS): ODPS 连接对象
        model_id (int): 模型 ID
        ds (str): 数据日期
        odpsex (ODPSExecutor): ODPS 执行器对象
        **params: 其他参数
    """

    def __init__(self, odps, ds, **params):
        """
        初始化 UpdateValidDates 类

        Args:
            odps (ODPS): ODPS 连接对象
            model_id (int): 模型 ID
            ds (str): 数据日期
            **params: 其他参数
        """
        self.odps = odps
        self.odpsex = ODPSExecutor(odps)
        self.ds = ds
        self.model_id = params['model_id']
        self.model_type = params['model_type']
        self.rule_table = params['rule_table']
        self.src_model_id_col = params['src_model_id_col']
        self.result_table = params['target_table']
        self.is_trace = params['is_trace']

    def update_history_data(self, end_date):
        """
        更新odps的结果表中有效历史数据的有效结束日期

        Args:
            end_date (str): 新的有效结束日期
        """
        sql = f"""
        UPDATE {self.result_table}
        SET end_dt = '{end_date}'
        WHERE model_id = '{self.model_id}' AND end_dt = '99991231' and statt_dt <= '{end_date}'
        """
        log_message(f"Updating valid end date to {end_date} for model {self.model_id}", 'info')
        self.odpsex.execute_ddl_dml(sql)

    def get_trace_and_modify_dt(self):
        """
        获取模型的修改日期和回溯日期

        Returns:
            tuple: 回溯日期和修改日期
        """
        model_manager = ModelManager(self.model_type, self.model_id, self.rule_table, self.src_model_id_col)
        # 获取模型的修改日期
        modify_dt = model_manager.get_modify_dt()
        if self.is_trace == '1':
            # 获取回溯中的回溯日期
            _, _, trace_dt, _ = model_manager.get_trace_info(status='02')
            return trace_dt, modify_dt
        else:
            return None, modify_dt

    def get_last_valid_date(self):
        """
        停用一段时间后再启用：获取结果表中昨日数据量=0且昨日之前的数据量>0，则取昨日之前数据中的最大日期

        Returns:
            str: 最大日期
        """
        yesterday = get_yesterday_date(self.ds)
        # 昨日数据量
        yesterday_count_sql = f"""
        SELECT COUNT(*)
        FROM {self.result_table}
        WHERE model_id = '{self.model_id}' AND end_dt = '99991231' AND statt_dt = '{yesterday}'
        """
        yesterday_count = self.odpsex.execute_select(yesterday_count_sql)

        # 昨日之前数据量
        before_yesterday_count_sql = f"""
        SELECT COUNT(*)
        FROM {self.result_table}
        WHERE model_id = '{self.model_id}' AND end_dt = '99991231' AND statt_dt < '{yesterday}'
        """
        before_yesterday_count = self.odpsex.execute_select(before_yesterday_count_sql)

        # 判断是否为停用一段时间后再启动的情况
        if yesterday_count.iloc[0][0] == 0 and before_yesterday_count.iloc[0][0] > 0:
            sql = f"""
            SELECT MAX(statt_dt)
            FROM {self.result_table}
            WHERE model_id = '{self.model_id}' AND end_dt = '99991231' AND statt_dt < '{yesterday}'
            """
            result = self.odpsex.execute_select(sql)
            return result.iloc[0][0]
        else:
            return None

    @timer
    def process_rule_update(self):
        """
        处理模型规则更新。只处理历史数据，不处理当天的数据（当天数据为计算时写入）
        1. 停用启用时更新
        2. 回溯时更新
        3. 模型规则更新时更新

        Returns:
            None
        """
        log_message(f"Start updating valid dates for model {self.model_id}", 'info')
        trace_dt, modify_dt = self.get_trace_and_modify_dt()
        last_valid_date = self.get_last_valid_date()

        # 只在检测到的第一天进行更新
        # 既有回溯也有修改，优先使用回溯日期
        if last_valid_date:
            # 存在停用一段时间后再启用的情况
            log_message(f"Found a model with disabled and enabled for model {self.model_id}", 'info')
            end_date = last_valid_date
            self.update_history_data(end_date)
            log_message(f"Updated valid dates for model {self.model_id} successfully", 'info')
        elif trace_dt:
            # 存在回溯任务
            if trace_dt == self.ds:
                # 今天有回溯任务
                log_message(f"Today is a trace task for model {self.model_id}", 'info')
                end_date = trace_dt
                self.update_history_data(end_date)
                log_message(f"Updated valid dates for model {self.model_id} successfully", 'info')
            else:
                log_message(f"Today is not a trace task for model {self.model_id}, skipping", 'info')
        elif modify_dt:
            # 存在模型规则更新
            if modify_dt == self.ds:
                # 今天有模型规则更新
                log_message(f"Today is a model rules update for model {self.model_id}", 'info')
                end_date = modify_dt
                self.update_history_data(end_date)
                log_message(f"Updated valid dates for model {self.model_id} successfully", 'info')
            else:
                log_message(f"Today is not a model rules update for model {self.model_id}, skipping", 'info')
        else:
            log_message(f"No trace task or model rules update found for model {self.model_id}, skipping", 'info')
