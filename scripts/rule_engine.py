'''
Author: <PERSON><PERSON><PERSON> He
Date: 2024-10-09 16:39:45
LastEditors: <EMAIL>
LastEditTime: 2025-07-21 16:43:42
FilePath: /opr_model_v2/scripts/rule_engine.py
Description: 根据配置规则，生成指标加工sql，加工结果写入临时窄表

所有模型使用同一个计算引擎，不需要每个模型都要重复实现，只需要在配置文件中定义好规则，即可实现不同模型之间的通用计算逻辑

Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
'''
import sys
from utils import get_date_range, log_message, timer, ODPSExecutor
from const import *

class RuleEngine:
    """规则引擎类，用于根据配置规则生成指标加工SQL并写入临时窄表

    Attributes:
        odps (ODPS): ODPS 客户端实例
        model_dict (dict): 模型配置字典
        ds (str): 数据日期
        is_trace (int): 是否为回溯任务 1-是，0-否
        odpsex (ODPSExecutor): ODPS 执行器实例
        temp_wide_table (str): 临时宽表名
        temp_narrow_table (str): 临时窄表名
        group_view (str): 圈群关系表视图名
        dim_table_map (dict): 维度表映射
    """

    def __init__(self, odps, model_dict, ds, is_trace):
        """初始化规则引擎

        Args:
            odps (ODPS): ODPS 客户端实例
            model_dict (dict): 模型配置字典
            ds (str): 数据日期
            is_trace (str): 是否为回溯任务 1-是，0-否
        """
        self.odps = odps
        self.model_dict = model_dict
        self.ds = ds
        self.is_trace = is_trace
        self.odpsex = ODPSExecutor(odps)
        # 定义临时宽表及窄表名
        if self.is_trace == '1':
            self.temp_wide_table = f"temp_{self.model_dict['modelId']}_wide_{self.ds}_trace"
            self.temp_narrow_table = f"temp_{self.model_dict['modelId']}_narrow_{self.ds}_trace"
        else:
            self.temp_wide_table = f"temp_{self.model_dict['modelId']}_wide_{self.ds}"
            self.temp_narrow_table = f"temp_{self.model_dict['modelId']}_narrow_{self.ds}"

        # 所有圈群关系表
        self.group_view = group_view
        # 维度表
        self.dim_table_map = dim_table_map

    def create_temp_narrow_table(self):
        """创建临时窄表: 存放指标加工后的结果"""
        drop_sql = f"DROP TABLE IF EXISTS {self.temp_narrow_table}"
        # 增加tx_num, usr_lt用于ltv
        create_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.temp_narrow_table} (
                obj_id string,
                cdt_id string,
                lab_val string,
                tx_num bigint,
                usr_lt double
            ) LIFECYCLE 7
        """
        self.odpsex.execute_ddl_dml(drop_sql)
        self.odpsex.execute_ddl_dml(create_sql)
        log_message("Created temp narrow table")

    def get_ids_from_group(self, group_no):
        """从圈群视图中获取对象ID列表及对象字段,返回组合后的字符串

        Args:
            group_no (str): 圈群编号

        Returns:
            str: 对象ID列表的SQL条件
        """
        log_message(f"Getting IDs for group: {group_no}")
        sql = f"SELECT obj_col, obj_id FROM {self.group_view} WHERE statt_dt = '{self.ds}' AND grp_no = '{group_no}'"
        group_ids = self.odpsex.execute_select(sql)
        # 将group_ids(DataFrame)转换为字符串列表
        if group_ids.empty:
            return "1=1"
        else:
            # 获取对象字段
            obj_col = group_ids['obj_col'].tolist()[0]
            obj_ids = ','.join([f"'{x}'" for x in group_ids['obj_id'].tolist()])
            return f"b.{obj_col} in ({obj_ids})"

    @timer
    def process_condition(self, condition):
        """处理单个条件并生成SQL片段

        Args:
            condition (dict): 条件字典

        Returns:
            str: 生成的SQL片段
        """
        # 计算日期范围
        period_val = condition.get("periodVal", "")
        period_tp = condition.get("periodTp", "")
        start_date, _ = get_date_range(self.ds, period_tp, period_val)

        # 获取对象表名
        obj_tp = condition.get("objTp")
        lab_table = condition.get("crspdTabEnNm")
        lab_column = condition.get("crspdFldEnNm")
        lab_type = condition.get("indxFlg", "")
        obj_col = condition.get("rltnpDimeFld")
        cdt_id = condition.get("cdtId")
        flt_obj_column = condition.get("fltCrspdFldEnNm")
        lab_dgre_en_nm = condition.get("labDgreEnNm")
        coef_val = condition.get("coefVal")
        # 20250324 condition中增加对象类型
        model_obj_cl = condition.get("modelObjCl")
        # 20250430 增加chnl_no，渠道可多选
        chnl_no = condition.get("chnlNo")
        # ltv 新增生命周期编码及新手期标签编号
        lfcy_cd = condition.get("lfcyCd")
        lfcy_lab_id = condition.get("lfcyLabId")
        lfcy_lab_id_tab = condition.get("lfcyLabIdTab")
        lfcy_lab_id_fld = condition.get("lfcyLabIdFld")
        lfcy_lab_id_rltnp_col = condition.get("lfcyLabIdRltnpCol")

        # 不同指标类型聚合，左关联不到取默认值0
        # 可选的指标类型包含：lab/indx_alone/indx_agg （attr类型不支持计算，只支持过滤）
        # 只支持lab/indx_alone/indx_agg，lab 为全量只取一天，indx_agg 为增量可周期内数据累加，indx_alone 为单个指标不可聚合
        select_clause = ""
        if lab_type == "lab" or lab_type == "indx_alone":
            select_clause = f"MAX(COALESCE(b.{lab_column}, '0'))"
        elif lab_type == "indx_agg":
            select_clause = f"SUM(COALESCE(b.{lab_column}, 0))"
        else:
            log_message(f"Invalid labType: {lab_type}", level="error")
            raise Exception(f"Invalid labType: {lab_type}")

        # 添加日期范围过滤条件
        if coef_val:
            # 针对LTV中的GMV，时间范围为本月（月初到当前）
            month_st_dt = self.ds[0:6] + "01"
            date_clause = f"b.statt_dt BETWEEN '{month_st_dt}' AND '{self.ds}'"
        else:
            date_clause = f"b.statt_dt BETWEEN '{start_date}' AND '{self.ds}'"

        # 判断过滤条件 (只能是attr类的字段)
        # 也需要映射为具体的字段
        # 增加flt_expression字段，若存在则使用flt_expression字段，否则使用flt_clause -- 针对rfm
        if condition["fltExpression"]:
            # ltv/gmv
            flt_clause = condition["fltExpression"]
        elif condition["fltLabNo"]:
            # rfm
            flt_clause = f"b.{flt_obj_column} {condition['fltClc']} '{condition['fltClcVal']}'"
        else:
            flt_clause = "1=1"

        # 根据objGrpTp处理不同的对象ID筛选: 01-单个对象，02-圈群
        # 增加lab_dgre_en_nm字段，用于区分不同对象
        if condition["objGrpTp"] == "01":
            if obj_tp == "01":
                # 若是单个对象产品，调用的是产品中心的接口，取到的产品编号为原产品编号
                obj_id_clause = f"b.orgnl_prod_no = '{condition['grpNo']}'"
            else:
                obj_id_clause = f"b.{lab_dgre_en_nm} = '{condition['grpNo']}'"
        elif condition["objGrpTp"] == "02":
            obj_id_clause = self.get_ids_from_group(condition["grpNo"])
        else:
            obj_id_clause = "1=1"

        # 增加chnl_no处理，对应产品表中的 tx_chnl_id -- 20250430新增
        if chnl_no:
            chnl_no_str = ','.join([f"'{x.strip()}'" for x in chnl_no.split(',')])
            chnl_clause = f"b.tx_chnl_id in ({chnl_no_str})"
        else:
            chnl_clause = "1=1"

        # 构造完整的SQL语句
        if coef_val:
            # 针对gmv，若有系数则为gmv （用户为生命周期阶段的用户，统计范围为本月） -- 系数在最后的表达式中有，此处无需系数
            # 增加有交易的用户数和笔数  -- 需为交易指标 （计算有交易的笔数，用户数单独处理）
            # 新手期对应指标应为日期类型，该字段类型有可能出问题 （未统一）
            sql = f"""
                INSERT INTO {self.temp_narrow_table}
                SELECT a.obj_id, '{cdt_id}', {select_clause} AS lab_val,
                SUM(case when b.{lab_column} > 0 then 1 else 0 end) AS tx_num,
                months_between(to_date(a.life_end_dt, 'yyyymmdd'), cast(c.{lfcy_lab_id_fld} as date)) as usr_lt  -- 用户寿命(月)
                FROM {lfcy_tables['rule_result']} a         -- 生命周期结果表 （强依赖）
                LEFT JOIN {lab_table} b                     -- 对象指标表
                ON a.obj_id = b.{obj_col} AND {date_clause} AND {flt_clause}    -- 日期范围和过滤条件
                LEFT JOIN {lfcy_lab_id_tab} c               -- 新手期指标对应表
                ON c.statt_dt = '{self.ds}' AND c.{lfcy_lab_id_rltnp_col} = a.obj_id
                WHERE a.statt_dt = '{self.ds}' AND a.model_id = '{lfcy_cd}'
                GROUP BY a.obj_id, '{cdt_id}', months_between(to_date(a.life_end_dt, 'yyyymmdd'), cast(c.{lfcy_lab_id_fld} as date))
            """
        elif obj_tp == "02":
            # 若为对象维度表，则无需关联
            sql = f"""
                INSERT INTO {self.temp_narrow_table}
                SELECT b.{obj_col}, '{cdt_id}', {select_clause} AS lab_val, null as tx_num, null as usr_lt
                FROM {lab_table} b        -- 对象指标表 （使用全量的，不管圈群与否，圈群在目标用户中过滤）
                WHERE b.statt_dt = '{self.ds}' AND {obj_id_clause} AND {flt_clause} AND {chnl_clause}
                GROUP BY b.{obj_col}, '{cdt_id}'
            """
        else:
            # 对象维度表作主表，左关联对象指标表，确保每个cdt_id都有值，关联不到的取默认值
            sql = f"""
                INSERT INTO {self.temp_narrow_table}
                SELECT a.{obj_col}, '{cdt_id}', {select_clause} AS lab_val, null as tx_num, null as usr_lt
                FROM {self.dim_table_map[model_obj_cl][0]} a        -- 对象维度表作主表 (根据对象类型使用不同的对象表)
                LEFT JOIN {lab_table} b                  -- 对象指标表
                ON a.{obj_col} = b.{obj_col} AND {date_clause} AND {obj_id_clause} AND {flt_clause} AND {chnl_clause}
                WHERE a.statt_dt = '{self.ds}'
                GROUP BY a.{obj_col}, '{cdt_id}'
            """

        log_message(f"SQL for cdt_id {cdt_id}:\n{sql}", level="debug")

        return sql

    @timer
    def process_model(self):
        """处理整个模型

        Returns:
            list: 生成的SQL语句列表
        """
        sql_statements = []
        for level in self.model_dict.get("levels", []):
            log_message(f"Processing level: {level.get('grdNm')}")
            for group in level.get("conditionGroups", []):
                conditions = group.get("conditions", [])
                for condition in conditions:
                    sql_statement = self.process_condition(condition)
                    sql_statements.append(sql_statement)

        return sql_statements

    def execute_sqls(self, sql_statements):
        """执行SQL语句

        Args:
            sql_statements (list): SQL语句列表
        """
        log_message("Executing SQL statements:")
        for sql in sql_statements:
            self.odpsex.execute_ddl_dml(sql)

    def get_cdt_ids(self):
        """从窄表中获取条件ID列表

        Returns:
            list: 条件ID列表
        """
        cdt_ids = self.odpsex.execute_select(f"SELECT DISTINCT cdt_id FROM {self.temp_narrow_table}")
        if cdt_ids.empty:
            log_message("No data found in narrow table", level="error")
            raise Exception("No data found in narrow table")
        else:
            # 将cdt_ids(DataFrame)转换为字符串列表
            cdt_ids = cdt_ids['cdt_id'].tolist()
            return cdt_ids

    def create_temp_wide_table(self, is_ltv=False):
        """创建临时宽表并填充数据

        该方法首先从窄表中获取条件ID列表，然后根据这些ID创建一个临时宽表，并将数据从窄表中聚合到宽表中。

        增加is_ltv，用于区分处理ltv中的计算逻辑
        """
        # 获取条件ID列表
        cdt_ids = self.get_cdt_ids()

        # 生成删除临时宽表的SQL语句
        drop_sql = f"DROP TABLE IF EXISTS {self.temp_wide_table}"

        # 生成创建临时宽表的SQL语句
        # 宽表中各个指标使用string而非double存放，否则表达式字符串判断不生效
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS {self.temp_wide_table} (
        """
        for cdt_id in cdt_ids:
            create_sql += f"  `{cdt_id}` string,"
        # 增加usr_num, tx_num, usr_lt用于ltv
        create_sql += "  obj_id string, usr_num bigint, tx_num bigint, usr_lt double"
        create_sql += ") LIFECYCLE 7;"

        # 生成插入数据到临时宽表的SQL语句
        insert_sql = f"""
        INSERT INTO {self.temp_wide_table}
        SELECT
        """
        for cdt_id in cdt_ids:
            insert_sql += f" MAX(CASE WHEN cdt_id = '{cdt_id}' THEN lab_val ELSE NULL END) AS {cdt_id}, "

        if is_ltv:
            # ltv需要计算usr_num, tx_num, usr_lt，且不按obj_id分组
            # 用户数为：限定为有交易的用户数
            insert_sql += "'all', COUNT(DISTINCT case when tx_num > 0 then obj_id else null end) AS usr_num, SUM(tx_num) AS tx_num, AVG(usr_lt) AS usr_lt, "
            insert_sql = insert_sql.rstrip(", ") + "\nFROM " + self.temp_narrow_table + "\nGROUP BY 'all';"
        else:
            insert_sql += " obj_id, null as usr_num, null as tx_num, null as usr_lt"
            insert_sql = insert_sql.rstrip(", ") + "\nFROM " + self.temp_narrow_table + "\nGROUP BY obj_id;"

        # 执行SQL语句
        log_message("Dropping existing temporary wide table if it exists...")
        self.odpsex.execute_ddl_dml(drop_sql)

        log_message("Creating temporary wide table...")
        self.odpsex.execute_ddl_dml(create_sql)

        log_message("Inserting data into temporary wide table...")
        self.odpsex.execute_ddl_dml(insert_sql)

        log_message(f"Successfully created and populated temporary wide table: {self.temp_wide_table}")
