
CREATE TABLE IF NOT EXISTS t_user_life_cycle_dfe_v2 (
    `id` BIGINT COMMENT '主键',
    `model_obj_cl` STRING COMMENT '模型对象分类',
    `biz_id` STRING COMMENT '业务编号',
    `lc_tp` STRING COMMENT '生命周期类型',
    `nm` STRING COMMENT '生命周期名称',
    `stu` STRING COMMENT '启用标识（01、启用；02、停用）',
    `rmk` STRING COMMENT '备注',
    `cret_id` STRING COMMENT '创建人ID',
    `cret_tm` DATETIME COMMENT '创建时间',
    `data_latest_dt` DATETIME COMMENT '数据最新时间',
    `modi_tm` DATETIME COMMENT '修改时间',
    `grp_id` STRING COMMENT '用户群编号'
) COMMENT 'ob-用户生命周期定义表' PARTITIONED BY (etl_dt STRING) lifecycle 36500;

CREATE TABLE IF NOT EXISTS t_user_life_cycle_grd_v2 (
    `id` BIGINT COMMENT '主键',
    `biz_id` STRING COMMENT '业务编号',
    `grd_cd` STRING COMMENT '生命周期等级类型',
    `grd_nm` STRING COMMENT '生命周期等级名称',
    `lc_tp` STRING COMMENT '生命周期类型',
    `num` BIGINT COMMENT '有效数量',
    `dfe` STRING COMMENT '定义',
    `cyc_tp` STRING COMMENT '统计窗口周期类型 mm月、dd日',
    `cyc_val` STRING COMMENT '统计窗口周期值',
    `pl` STRING COMMENT '计算优先级（优先级最高的为潜客阶段）',
    `is_gnu` STRING COMMENT '是否获取新用户（1、是；2、否）',
    `cret_tm` DATETIME COMMENT '创建时间'
) COMMENT 'ob-用户生命周期等级定义表' PARTITIONED BY (etl_dt STRING) lifecycle 36500;

CREATE TABLE IF NOT EXISTS t_user_life_cycle_grd_cdt_v2 (
    `id` BIGINT COMMENT '主键',
    `biz_id` STRING COMMENT '业务编号',
    `cdt_no` STRING COMMENT '条件组编号',
    `parnt_cdt_no` STRING COMMENT '父条件组编号',
    `grd_cd` STRING COMMENT '生命周期等级类型',
    `cdt_tp` STRING COMMENT '组间关系类型 且01 或02',
    `cdt_desc` STRING COMMENT '组内描述 根据组内条件组装好的表达式 条件编号1 + 条件编号2 > 0 ',
    `lvl` STRING COMMENT '层级',
    `rid` STRING COMMENT 'rid',
    `cret_tm` DATETIME COMMENT '创建时间'
) COMMENT 'ob-用户生命周期等级配置条件组信息表' PARTITIONED BY (etl_dt STRING) lifecycle 36500;

CREATE TABLE IF NOT EXISTS t_user_life_cycle_grd_cdt_lab_v2 (
    `id` BIGINT COMMENT '主键',
    `biz_id` STRING COMMENT '业务编号',
    `grd_cd` STRING COMMENT '生命周期等级类型',
    `cdt_id` STRING COMMENT '条件编号',
    `cdt_no` STRING COMMENT '条件组编号',
    `obj_tp` STRING COMMENT '条件所属对象类型（用户、产品、活动）',
    `prod_tp` STRING COMMENT '产品类型（产品、积分、权益）',
    `prod_obj_tp` STRING COMMENT '产品对象类型（单个对象、对象群）',
    `grp_no` STRING COMMENT '所属群编号',
    `grp_nm` STRING COMMENT '所属群名称',
    `lab_no` STRING COMMENT '指标编号',
    `lab_nm` STRING COMMENT '指标名称',
    `clc` STRING COMMENT '计算判断符',
    `clc_val` STRING COMMENT '判断值',
    `lvl` STRING COMMENT '层级',
    `rid` STRING COMMENT 'rid',
    `cret_tm` DATETIME COMMENT '创建时间',
    `clc_val_tp` STRING COMMENT '判断值类型',
    `code_tp_cd` STRING COMMENT '标签枚举值编码'
) COMMENT 'ob-用户生命周期配置条件信息表' PARTITIONED BY (etl_dt STRING) lifecycle 36500;

CREATE TABLE IF NOT EXISTS t_user_life_cycle_uad_v2 (
    `id` BIGINT COMMENT '主键',
    `biz_id` STRING COMMENT '业务编号',
    `grd_cd` STRING COMMENT '生命周期等级类型',
    `uad_tp` STRING COMMENT '升降级类型 01 升级 02 降级',
    `to_grd_cd` STRING COMMENT '去向生命周期等级类型 ',
    `cret_tm` DATETIME COMMENT '创建时间'
) COMMENT 'ob-用户生命周期等级升降级配置表' PARTITIONED BY (etl_dt STRING) lifecycle 36500;
