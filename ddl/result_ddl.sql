
-- 结果表需为事务表（因为涉及有效结束日期的更新）
-- 20241112: 统一结果表字段命名，以便统一查询sql
-- 20241218: 结果表历史数据仅保留一年，防止holo查询分区超出限制问题，以及历史数据清理 （增加lifecycle 365）
-- 20250325: 结果表中增加字段：模型对象分类 01-个人用户 02-企业用户 03-企网通用户 04-聚合收单商户 05-本地生活商户
-- 20250710: 生命周期结果表中增加结束日期，规则为：若当前为衰退期，则取最早进入衰退期的日期；若不为衰退期，则取当前日期 - 用于计算用户寿命

-- 生命周期结果表
drop table if exists adm_opma_model_lfcy_rslt_dd_v2;

create table if not exists adm_opma_model_lfcy_rslt_dd_v2 (
    model_obj_cl     varchar(100) comment '模型对象分类',
    model_nm         varchar(300) comment '模型名称',
    obj_id           varchar(100) comment '对象编号',
    lvl_cd           varchar(32)  comment '生命周期等级代码',
    lvl_dsc          varchar(100) comment '生命周期等级描述',
    chg_dt           varchar(8) comment '变动日期',
    chg_tp_cd        varchar(32)  comment '变动类型代码',
    chg_tp_dsc       varchar(100) comment '变动类型描述',
    before_lvl_cd    varchar(32) comment '变动前生命周期等级代码',
    before_lvl_dsc   varchar(100) comment '变动前生命周期等级描述',
    valid_matr_dt    varchar(8) comment '有效到期日期',
    life_end_dt      varchar(8) comment '生命周期结束日期',
    rmrk             varchar(300) comment '备注',
    start_dt         varchar(8) comment '有效开始日期',
    end_dt           varchar(8) comment '有效结束日期',
    etl_job          varchar(200) comment '任务编号',
    etl_src_tab      varchar(100) comment '来源主表'
) comment '用户生命周期结果表'
partitioned by (
    statt_dt         varchar(8)  comment '统计日期',
    model_id         varchar(100) comment '模型编号'
) tblproperties ("transactional" = "true")
lifecycle 365;

-- AARRR结果表
drop table if exists adm_opma_model_aarrr_rslt_dd_v2;

create table if not exists adm_opma_model_aarrr_rslt_dd_v2 (
    model_obj_cl         varchar(100) comment '模型对象分类',
    model_nm             varchar(300) comment '模型名称',
    obj_id               varchar(100) comment '对象编号',
    lvl_cd               varchar(32)  comment 'aarrr等级代码',
    lvl_dsc              varchar(100) comment 'aarrr等级描述',
    chg_dt               varchar(8) comment '变动日期',
    before_lvl_cd        varchar(32) comment '变动前aarrr等级代码',
    before_lvl_dsc       varchar(100) comment '变动前aarrr等级描述',
    rmrk                 varchar(300) comment '备注',
    start_dt             varchar(8) comment '有效开始日期',
    end_dt               varchar(8) comment '有效结束日期',
    etl_job              varchar(200) comment '任务编号',
    etl_src_tab          varchar(100) comment '来源主表'
) comment 'aarrr结果表'
partitioned by (
    statt_dt             varchar(8)  comment '统计日期',
    model_id             varchar(100) comment '模型编号'
)tblproperties ("transactional" = "true")
lifecycle 365;

-- RFM结果表
drop table if exists adm_opma_model_rfm_rslt_dd_v2;

create table if not exists adm_opma_model_rfm_rslt_dd_v2 (
    model_obj_cl     varchar(100) comment '模型对象分类',
    model_nm         varchar(300) comment '模型名称',
    obj_id           varchar(100) comment '对象编号',
    r_flg_bit        varchar(1)   comment 'r标志位',
    f_flg_bit        varchar(1)   comment 'f标志位',
    m_flg_bit        varchar(1)   comment 'm标志位',
    rfm_cd           varchar(3)   comment 'rfm编码',
    rfm_nm           varchar(100) comment 'rfm名称',
    rmrk             varchar(300) comment '备注',
    start_dt         varchar(8) comment '有效开始日期',
    end_dt           varchar(8) comment '有效结束日期',
    etl_job          varchar(200) comment '任务编号',
    etl_src_tab      varchar(100) comment '来源主表'
) comment 'rfm结果表'
partitioned by (
    statt_dt         varchar(8)  comment '统计日期',
    model_id         varchar(100) comment '模型编号'
)tblproperties ("transactional" = "true")
lifecycle 365;


-- LTV结果表 （将GMV合并进去了）
drop table if exists adm_opma_model_ltv_rslt_dd_v2;

create table if not exists adm_opma_model_ltv_rslt_dd_v2 (
    model_obj_cl     varchar(100) comment '模型对象分类',
    model_nm         varchar(300) comment '模型名称',
    lfcy_tp_cd       varchar(100) comment '生命周期类型代码',
    lfcy_tp_dsc      varchar(300) comment '生命周期类型描述',
    ltv_val          decimal(20, 2) comment 'ltv值',
    lt_val           decimal(20, 2) comment 'lt值-月平均寿命',
    apru_val         decimal(20, 2) comment 'apru值-用户平均营收',
    gmv_val          decimal(20, 2) comment 'gmv值-交易流量',
    tx_num           bigint        comment '交易笔数',
    tx_usr_cnt       bigint        comment '交易用户数',
    rmrk             varchar(300) comment '备注',
    start_dt         varchar(8) comment '有效开始日期',
    end_dt           varchar(8) comment '有效结束日期',
    etl_job          varchar(200) comment '任务编号',
    etl_src_tab      varchar(100) comment '来源主表'
) comment 'ltv结果表'
partitioned by (
    statt_dt         varchar(8)  comment '统计日期',
    model_id         varchar(100) comment '模型编号'
)tblproperties ("transactional" = "true")
lifecycle 365;

-- CAC结果表 (暂时还未确定，不处理)
drop table if exists adm_opma_model_cac_rslt_dd_v2;

create table if not exists adm_opma_model_cac_rslt_dd_v2 (
    model_nm         varchar(300) comment '模型名称',
    consm_amt        decimal(20, 2) comment '消费金额',
    consm_usr_cnt    bigint        comment '消费用户数',
    cac_val          decimal(20, 2) comment 'cac值',
    rmrk             varchar(300) comment '备注',
    start_dt         varchar(8) comment '有效开始日期',
    end_dt           varchar(8) comment '有效结束日期',
    etl_job          varchar(200) comment '任务编号',
    etl_src_tab      varchar(100) comment '来源主表'
) comment 'cac结果表'
partitioned by (
    statt_dt         varchar(8)  comment '统计日期',
    model_id         varchar(100) comment '模型编号'
)tblproperties ("transactional" = "true")
lifecycle 365;
