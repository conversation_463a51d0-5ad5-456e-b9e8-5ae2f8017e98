
-- 在holo中对结果表创建外表，以供运营端访问 （使用v2版本的表）
import foreign schema opma_uat1 limit to (
    adm_opma_model_lfcy_rslt_dd_v2,
    adm_opma_model_aarrr_rslt_dd_v2,
    adm_opma_model_rfm_rslt_dd_v2,
    adm_opma_model_gmv_rslt_dd_v2,
    adm_opma_model_cac_rslt_dd_v2,
    adm_opma_model_ltv_rslt_dd_v2
) from server odps_server into public options (if_table_exist 'update');


-- 基于这些结果表，运营端会在holo中再创建视图，进行字段映射后供页面展示
-- 对应的view为 v_xxx，不带v2，过滤条件为：最新的日期数据
create or replace v_adm_opma_model_lfcy_rlst_dd as
select * from adm_opma_model_lfcy_rslt_dd_v2
where 取最新日期数据
