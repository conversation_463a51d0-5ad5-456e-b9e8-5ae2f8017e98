
CREATE TABLE IF NOT EXISTS t_user_rfm_v2 (
    `id` BIGINT COMMENT '主键',
    `model_obj_cl` STRING COMMENT '模型对象分类',
    `rfm_cd` STRING COMMENT 'rfm模型类型',
    `nm` STRING COMMENT 'rfm模型名称',
    `trg_cd` STRING COMMENT 'rfm模型目标类型（产品类、活动类）',
    `prod_tp` STRING COMMENT '产品类型（权益、积分、产品）',
    `obj_tp` STRING COMMENT '对象类型（单个对象、对象群）',
    `obj_cd` STRING COMMENT '对象编码',
    `obj_nm` STRING COMMENT '对象名称',
    `stu` STRING COMMENT '启用标识（00、待启用；01、启用；02、停用）',
    `rmk` STRING COMMENT '备注',
    `cret_id` STRING COMMENT '创建人ID',
    `cret_tm` DATETIME COMMENT '创建时间',
    `data_latest_dt` DATETIME COMMENT '数据最新时间',
    `modi_tm` DATETIME COMMENT '修改时间',
    `flt_express` STRING COMMENT '过滤表达式'
) COMMENT 'ob-RFM模型管理表' PARTITIONED BY (etl_dt STRING) lifecycle 36500;

CREATE TABLE IF NOT EXISTS t_user_rfm_cdt_v2 (
    `id` BIGINT COMMENT '主键',
    `rfm_cd` STRING COMMENT 'RFM模型类型',
    `cdt_no` STRING COMMENT '条件组编号',
    `parnt_cdt_no` STRING COMMENT '父条件组编号',
    `rule_tp` STRING COMMENT '规则类型（进度、频度、额度）',
    `cdt_in_tp` STRING COMMENT '组内关系类型 且01 或02 累加03',
    `cdt_desc` STRING COMMENT '组内描述 根据组内条件组装好的表达式 条件编号1 + 条件编号2 > 0 ',
    `lvl` STRING COMMENT '层级',
    `rid` STRING COMMENT 'rid'
) COMMENT 'ob-RFM条件组信息表' PARTITIONED BY (etl_dt STRING) lifecycle 36500;

CREATE TABLE IF NOT EXISTS t_user_rfm_cdt_lab_v2 (
    `id` BIGINT COMMENT '主键',
    `rfm_cd` STRING COMMENT 'RFM模型类型',
    `rule_tp` STRING COMMENT '规则类型（进度、频度、额度）',
    `cdt_id` STRING COMMENT '条件编号',
    `cdt_no` STRING COMMENT '条件组编号',
    `ccl_val` STRING COMMENT '周期值',
    `ccl_tp` STRING COMMENT '周期类型',
    `lab_no` STRING COMMENT '指标编号',
    `lab_nm` STRING COMMENT '指标名称',
    `clc` STRING COMMENT '计算判断符',
    `clc_val` STRING COMMENT '判断值',
    `lvl` STRING COMMENT '层级',
    `rid` STRING COMMENT 'rid',
    `clc_val_tp` STRING COMMENT '判断值类型',
    `code_tp_cd` STRING COMMENT '标签枚举值编码'
) COMMENT 'ob-RFM条件信息表' PARTITIONED BY (etl_dt STRING) lifecycle 36500;

CREATE TABLE IF NOT EXISTS t_user_rfm_flt_cdt_v2 (
    `id` BIGINT COMMENT '主键',
    `rfm_cd` STRING COMMENT 'RFM模型类型',
    `flt_lab_no` STRING COMMENT '过滤标签编号',
    `flt_lab_nm` STRING COMMENT '过滤标签名称',
    `flt_clc` STRING COMMENT '过滤判断符',
    `flt_clc_val` STRING COMMENT '过滤判断值',
    `cdt_in_tp` STRING COMMENT '组内关系类型 且01 或02',
    `flt_code_tp_cd` STRING COMMENT '过滤标签枚举值编码',
    `flt_clc_val_tp` STRING COMMENT '过滤判断值类型'
) COMMENT 'ob-RFM过滤条件信息表' PARTITIONED BY (etl_dt STRING) lifecycle 36500;
