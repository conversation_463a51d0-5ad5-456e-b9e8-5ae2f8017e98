create table if not exists t_user_aa_rrr_v2(
`id`                            bigint comment '主键',
`model_obj_cl`                  string comment '模型对象分类',
`ar_cd`                         string comment 'aarrr模型类型',
`nm`                            string comment 'aarrr模型名称',
`stu`                           string comment '启用标识（01、启用；02、停用）',
`rmk`                           string comment '备注',
`cret_id`                       string comment '创建人id',
`cret_tm`                       datetime comment '创建时间',
`data_latest_dt`                datetime comment '数据最新时间',
`modi_tm`                       datetime comment '修改时间'
)
comment 'ob-aarrr模型定义表'
partitioned by (etl_dt string)
lifecycle 36500;


create table if not exists t_user_aa_rrr_grd_v2(
`id`                            bigint comment '主键',
`grd_cd`                        string comment 'aarrr等级类型',
`grd_nm`                        string comment 'aarrr等级名称',
`ar_cd`                         string comment 'aarrr模型类型',
`pl`                            string comment '计算优先级（优先级最高的为潜客阶段）',
`is_uc_flg`                     string comment '是否无条件符合标志（1、是；2、否）',
`is_gnu`                        string comment '是否获取新用户（1、是；2、否）',
`dfe`                           string comment '定义',
`is_clc_flg`                    string comment '是否参与计算标志（1、是；2、否）'
)
comment 'ob-aarrr等级定义表'
partitioned by (etl_dt string)
lifecycle 36500;


create table if not exists t_user_aa_rrr_cdt_v2 (
    `id` bigint comment '主键',
    `ar_cd` string comment 'aarrr模型类型',
    `cdt_no` string comment '条件组编号',
    `grd_cd` string comment 'aarrr等级类型',
    `cdt_tp` string comment '组间关系类型 且01 或02',
    `cdt_in_tp` string comment '组内关系类型 且01 或02 累加03',
    `cdt_desc` string comment '组内描述 根据组内条件组装好的表达式 条件编号1 + 条件编号2 > 0 ',
    `parnt_cdt_no` string comment '父条件组编号',
    `lvl` string comment '层级',
    `rid` string comment 'rid'
) comment 'ob-aarrr等级配置条件组信息表' partitioned by (etl_dt string) lifecycle 36500;


create table if not exists t_user_aa_rrr_cdt_lab_v2(
`id`                            bigint comment '主键',
`ar_cd`                         string comment 'aarrr模型类型',
`grd_cd`                        string comment 'aarrr等级类型',
`cdt_id`                        string comment '条件编号',
`cdt_no`                        string comment '条件组编号',
`obj_tp`                        string comment '条件所属对象类型（用户、产品、活动）',
`obj_tp_cl`                     string comment '对象细类（权益、积分、产品）',
`prod_obj_tp`                   string comment '产品对象类型（单个对象、对象群）',
`grp_no`                        string comment '所属群编号',
`grp_nm`                        string comment '所属群名称',
`grp_obj_no`                    string comment '所属群对象编号',
`ccl_val`                       string comment '周期值',
`ccl_tp`                        string comment '周期类型',
`flt_lab_no`                    string comment '过滤标签编号',
`flt_lab_nm`                    string comment '过滤标签名称',
`flt_clc`                       string comment '过滤判断符',
`flt_clc_val`                   string comment '过滤判断值',
`lab_no`                        string comment '指标编号',
`lab_nm`                        string comment '指标名称',
`clc`                           string comment '计算判断符',
`clc_val`                       string comment '判断值',
`lvl`                           string comment '层级',
`rid`                           string comment 'rid',
`clc_val_tp`                    string comment '判断值类型',
`code_tp_cd`                    string comment '标签枚举值编码',
`flt_code_tp_cd`                string comment '过滤标签枚举值编码',
`flt_clc_val_tp`                string comment '过滤判断值类型',
`chnl_no`                       string comment '渠道编号'
)
comment 'ob-aarrr等级配置条件信息表'
partitioned by (etl_dt string)
lifecycle 36500;


