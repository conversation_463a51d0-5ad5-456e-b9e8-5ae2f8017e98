
CREATE TABLE IF NOT EXISTS adm_opma_pub_lab_map_confi_tab (
    lab_id VARCHAR(60) COMMENT '标签编号',
    lab_nm VARCHAR(200) COMMENT '标签名称',
    lab_dgre_en_nm VARCHAR(200) COMMENT '标签粒度英文名称',
    lab_dgre_ch_nm VARCHAR(200) COMMENT '标签粒度中文名称',
    lab_val_tp_dsc VARCHAR(200) COMMENT '标签值类型描述',
    lab_1_lvl_cl VARCHAR(100) COMMENT '标签一级分类',
    lab_1_lvl_cl_dsc VARCHAR(400) COMMENT '标签一级分类描述',
    lab_2_lvl_cl VARCHAR(100) COMMENT '标签二级分类',
    lab_2_lvl_cl_dsc VARCHAR(400) COMMENT '标签二级分类描述',
    lab_st_cd VARCHAR(100) COMMENT '标签状态代码',
    crspd_tab_en_nm VARCHAR(100) COMMENT '对应表英文名称',
    crspd_tab_cn_nm VARCHAR(200) COMMENT '对应表中文名称',
    crspd_fld_en_nm VARCHAR(100) COMMENT '对应字段英文名称',
    crspd_fld_cn_nm VARCHAR(200) COMMENT '对应字段中文名称',
    rltnp_dime_fld VARCHAR(200) COMMENT '关联维度字段',
    rcnt_modi_dt DATE COMMENT '最近修改日期',
    rcnt_modi_prsn_nm VARCHAR(200) COMMENT '最近修改人名称',
    rmrk VARCHAR(200) COMMENT '备注',
    indx_flg VARCHAR(10) COMMENT '指标标识',                       -- 涉及指标加工的逻辑，indx必须是可sum的数字型(后续是否考虑区分哪些是可聚合的，哪些是不能的)，其他置为lab
    dflt_val VARCHAR(50) COMMENT '默认值',
    is_map_flg VARCHAR(1) COMMENT '是否映射标志',
    etl_job VARCHAR(200) COMMENT '任务编号',
    etl_src_tab VARCHAR(100) COMMENT '来源主表'
) PARTITIONED BY (
    statt_dt VARCHAR(8) COMMENT '统计日期'
) STORED AS ALIORC TBLPROPERTIES ('comment' = '标签映射配置表');



-- todo：现有的指标标签表的数据需要更新调整（比如周期等级字段，要从indx改为lab，否则计算时会出问题）
-- 数据已更新为最新的，标签类型由之前的attr/lab/indx，改为attr/lab/indx_alone/indx_agg
