CREATE TABLE IF NOT EXISTS t_user_ltv_v2 (
    `id` BIGINT COMMENT '主键',
    `ltv_cd` STRING COMMENT 'LTV模型编码',
    `model_obj_cl` STRING COMMENT '模型对象分类 01-个人用户(UID) 02-企业用户(EID) 03-企网通用户(PID) 04-收单商户 05-本地生活商户(多级渠道编号)',
    `lc_cd` STRING COMMENT '生命周期模型编码',
    `lt_id` STRING COMMENT '用户寿命开始计算时间标签id',
    `nm` STRING COMMENT '模型名称',
    `stu` STRING COMMENT '启用标识（00、待启用；01、启用；02、停用）',
    `rmk` STRING COMMENT '备注',
    `cret_id` STRING COMMENT '创建人ID',
    `cret_tm` DATETIME COMMENT '创建时间',
    `data_latest_dt` DATETIME COMMENT '数据最新日期',
    `modi_tm` DATETIME COMMENT '修改时间'
) COMMENT 'ob-LTV模型定义表' PARTITIONED BY (etl_dt STRING) lifecycle 36500;

CREATE TABLE IF NOT EXISTS t_user_ltv_gmv_v2 (
    `id` BIGINT COMMENT '主键',
    `ltv_cd` STRING COMMENT 'LTV模型编码',
    `gmv_no` STRING COMMENT 'gmv规则组编号',
    `gmv_desc` STRING COMMENT 'GMV计算组装好的表达式，如： 计算组1*0.3 +计算组2*0.3',
    `modi_tm` DATETIME COMMENT '修改时间'
) COMMENT 'ob-LTV模型GMV定义' PARTITIONED BY (etl_dt STRING) lifecycle 36500;

CREATE TABLE IF NOT EXISTS t_user_ltv_gmv_grp_v2 (
    `id` BIGINT COMMENT '主键',
    `ltv_cd` STRING COMMENT 'LTV模型编码',
    `gmv_no` STRING COMMENT 'gmv规则组编号',
    `cal_no` STRING COMMENT '计算组编号',
    `cal_cft` STRING COMMENT '计算系数',
    `cal_desc` STRING COMMENT '组内计算描述 根据组内计算组装好的表达式，如： 指标1*0.3 + 指标2*0.5',
    `modi_tm` DATETIME COMMENT '修改时间'
) COMMENT 'ob-LTV模型GMV规则组' PARTITIONED BY (etl_dt STRING) lifecycle 36500;

CREATE TABLE IF NOT EXISTS t_user_ltv_gmv_cdt_v2 (
    `id` BIGINT COMMENT '主键',
    `ltv_cd` STRING COMMENT 'LTV模型编码',
    `gmv_no` STRING COMMENT 'gmv规则组编号',
    `cal_no` STRING COMMENT '计算组编号',
    `cdt_no` STRING COMMENT '规则计算编号',
    `cdt_tp` STRING COMMENT '组间关系类型 且and 或or',
    `cal_lab` STRING COMMENT '计算指标',
    `cal_nm` STRING COMMENT '计算名称',
    `cal_tab` STRING COMMENT '计算表名',
    `cal_cft` STRING COMMENT '计算系数',
    `cdt_desc` STRING COMMENT '组内描述 根据组内条件组装好的表达式，如：usr_id + addr > 0',
    `modi_tm` DATETIME COMMENT '修改时间'
) COMMENT 'ob-LTV模型GMV规则计算' PARTITIONED BY (etl_dt STRING) lifecycle 36500;

CREATE TABLE IF NOT EXISTS t_user_ltv_gmv_cdt_lab_v2 (
    `id` BIGINT COMMENT '主键',
    `ltv_cd` STRING COMMENT 'LTV模型编码',
    `gmv_no` STRING COMMENT 'gmv规则组编号',
    `cdt_no` STRING COMMENT '规则计算编号',
    `lab_cd` STRING COMMENT '指标编码',
    `lab_nm` STRING COMMENT '指标名称',
    `lab_tp` STRING COMMENT '指标类型',
    `clc` STRING COMMENT '计算判断符',
    `clc_val` STRING COMMENT '判断值',
    `modi_tm` DATETIME COMMENT '修改时间'
) COMMENT 'ob-LTV模型GMV规则计算条件' PARTITIONED BY (etl_dt STRING) lifecycle 36500;
