'''
Author: <PERSON><PERSON><PERSON> He
Date: 2024-11-18 14:30:20
LastEditors: <EMAIL>
LastEditTime: 2024-11-18 18:01:31
FilePath: /opr_model_v2/trace_flow/end.py
Description: 判断是否继续执行回溯任务  （重要：在实际执行时，不能有注释，否则会报错）

1. 无回溯任务，休眠结束后，超过23点,则停止循环
2. 达到最大循环次数,则停止循环
3. 若22点开始执行，执行到第二天，并且未超过最大循环次数，则检查不到无法停止，继续当前循环任务
4. 无任务时休眠，有任务时不休眠
5. 当前循环任务结束，第二天重新启动循环任务检查 （依赖上一周期-本节点） -- 存在一定延迟及任务堆积

判断条件：True: 继续循环，False: 停止循环

Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
'''
from datetime import datetime

def should_continue():
    current_hour = datetime.now().hour

    if current_hour > 23:
        return False
    elif ${dag.loopTimes} > 127:
        return False
    else:
        return True


if __name__ == "__main__":
    result = should_continue()
    print(result)
