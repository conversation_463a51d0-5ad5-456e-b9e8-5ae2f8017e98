#!/bin/bash
###
 # @Author: <PERSON><PERSON><PERSON> He
 # @Date: 2024-11-18 14:44:06
 # @LastEditors: <EMAIL>
 # @LastEditTime: 2024-11-18 18:01:55
 # @FilePath: /opr_model_v2/trace_flow/sh_no_tasks.sh
 # @Description: 若没有待回溯任务,则休眠一段指定时间，避免频繁检查
 #
 # Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
###

# 记录开始时间
start_time=$(date "+%Y-%m-%d %H:%M:%S")
echo "开始时间: $start_time"

echo "没有待回溯任务,休眠一段时间..."

# 休眠指定时间
sleep 1m 30s

# 记录结束时间
end_time=$(date "+%Y-%m-%d %H:%M:%S")
echo "结束时间: $end_time"

 # 计算运行时间
 run_time=$(echo "$end_time - $start_time" | bc -l)
 echo "已休眠: $run_time"
