'''
Author: <PERSON><PERSON><PERSON> He
Date: 2024-11-18 14:30:20
LastEditors: <EMAIL>
LastEditTime: 2024-11-18 18:00:33
FilePath: /opr_model_v2/trace_flow/check_trace_task.py
Description: 检查是否有待回溯的任务 (弃用)

(当前环境没有pymysql，无法执行，不使用该方法，改为使用odps sql -- 增加一个作业来同步，每次同步时清空现有表所有数据)

Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
'''
import sys
import pymysql

# python2 编码问题
reload(sys)
sys.setdefaultencoding('utf-8')

def check_trace_tasks():
    """检查是否有待回溯的任务.

    Returns:
        bool: 是否存在待回溯任务
    """
    try:
        # 建立数据库连接
        conn = pymysql.connect(
            host='ob-cn-n1z1034460001-00.rwlb.rds.aliyuncs.com',
            port=3306,
            user='oppl_uat1',
            password='xxxx',
            database='ob_oppl_uat1'
        )
        cursor = conn.cursor()

        # 执行查询
        sql = """
        select count(1) from t_model_trace_inf
        where trace_st = '01'
        and model_tp = 'LFCY'
        """
        cursor.execute(sql)
        count = cursor.fetchone()[0]

        if count > 0:
            print(f"发现{count}个待回溯任务")
            return count
        else:
            print("没有待回溯任务")
            return 0

    except Exception as e:
        print(f"执行出错: {e}")
        raise
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    try:
        has_tasks = check_trace_tasks()
        print(has_tasks)
    except Exception:
        sys.exit(2)


