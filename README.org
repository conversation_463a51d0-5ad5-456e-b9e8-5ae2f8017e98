#+TITLE: 动态模型规则计算引擎
#+OPTIONS: ^:nil toc:t

* 概述
功能名：动态模型规则计算引擎

业务人员可在前端配置多个模型，每个模型中有多个等级或阶段，每个阶段可根据业务场景，配置不同的指标计算规则（可指定时间区间/圈群对象等）及判断规则，后端根据所配置规则进行指标数据处理，再根据判断规则判断，输出模型结果数据，提供即时反馈，用于后续分析及圈群营销

当前模型：
- 生命周期模型 LFCY
- 海盗模型/增长黑客理论模型 AARRR
- 客户价值分析模型 RFM
- 商品交易总额模型 GMV
- 用户生命周期价值模型 LTV
- 客户获取成本模型 CAC

* 模型说明

** LFCY
描述某个实体在存在期间经历不同阶段的过程，可用于多个场景：
- 客户生命周期
  - 潜客期
  - 新手期
  - 成长期
  - 成熟期
  - 衰退期
  - 高危期
  - 睡眠期
- 产品生命周期
- ....

** AARRR
识别并优化增长黑客的关键路径，实现快速用户基础增长
- Acquisition 获取：吸引新用户策略
- Activation 激活：完成初始转化
- Retention 留存：保持用户活跃度
- Revenue 收入：获取收入，免费服务中获取收益
- Referral 推荐：向他人推荐产品或服务

** RFM
衡量客户价值，预测客户未来活动。
- R 最近一次消费：距离现在的时间长度，越小价值可能更高
- F 消费频率：高频率代表高忠诚度客户
- M 消费金额：高金额代表高价值客户

** GMV
一定时间内平台上的所有订单的商品总额，无论是否完成支付或退款。仅反映平台的交易规模和活跃程序，并非平台实际收入

** LTV
用户在使用产品或服务期间，给企业带来的净收入或价值。 与cac对比，优化营销策略，区分高价值客户与低价值客户，特定营销

** CAC
企业为了吸引新客户所花费的平均成本。 优化营销策略  (获利：ltv > cac)

计算公式： 营销总支出/新客数量

* 整体逻辑
后端数据处理逻辑：
1. 读取配置表，获取各模型各等级的配置规则
   1) 不依赖于数据中台，以便重跑或回溯
   2) 空值在同步时全部转化为null，利于判断
2. 根据所配置的指标及规则，计算出分析主体对象的具体指标
   1) 通过pyodps建立数据仓库接口，高效获取指标表及维度表数据
   2) 设计计算逻辑，自动处理数据
3. 根据计算出的指标，结合判断规则，生成各等级用户数据


版本v2的处理逻辑：
1. 查询模型配置规则，转换为json对象
2. 根据规则，计算出每条规则的指标数据，存到临时窄表
   1. 按模型并行，每个临时表与当前model关联，互不影响
   2. 临时指标表： temp + modelid + narrow/wide + ds
   3. 临时目标表： temp + modelid + target/target_user + ds
3. 将临时窄表转换为临时宽表 （按当事人维度表来处理，一个用户一条，列名根据指标来创建）
4. 根据优化级判断，写入结果至临时目标表（事务表），按等级顺序依次更新
5. 再将临时目标表，关联模型后，写入最终目标表（事务表） -- 后续涉及有效结束日期更新

* 特殊逻辑
各模型特有的逻辑

** LFCY
涉及升级/降级/保级，满足条件后立刻升级，不满足条件达到指定周期数才进行降级。每个等级配置不同的升级/降级路径

升级可配置多个，降级只能配置一个

目标用户有前后依赖：因为升降级，需获取当前等级来按配置去向升降

统计周期在等级表，后面关联放到各个条件中 （等级表也放，以用于判断该等级的有效期） -- 仅生命周期，其他为空

计算逻辑：
1. 按等级优先级，依次处理各个等级，只处理满足规则的用户
   1. 先获取该等级满足规则的目标用户
      1. 若是新增用户，则只取新用户，类型为01：当日用户表排除昨日结果表用户
      2. 否则，获取昨日可升级到该等级的用户，类型为01；当日临时表中可升级到该等级的用户，类型为01；昨日该等级的用户，类型为02
      3. 01-升级 02-保级延期/保级不延期/降级（昨日该等级）
   2. 满足规则的目标用户，与规则指标表对比，得到最终满足规则的用户，更新目标临时表
      1. 目标用户类型为01+02，一并处理，都属于满足规则的用户类型
   3. 所有等级处理完成后，临时目标表中的是最终满足规则的各等级用户
2. 再处理所有不满足规则的用户：即从类型为02中，排除掉满足规则的用户，剩下的就是不满足规则的用户
   1. 关联降级表：获取可降级到的目标等级
   2. 判断该等级的原始有效期当天是否已经过期，若过期则标记为降级用户，同时更新有效期和类型
   3. 若不过期，则保持原来的等级和有效期不变
   4. （满足规则的分等级处理，不满足规则的批量一次性处理）
3. 所有用户等级更新完成
4. 写入目标表

目标用户类型：
- 01:可升级的用户  -- 新用户+当日可升到该等级用户+昨日可升到该等级用户
  - 有可升级到的去向等级字段，更新时直取
- 02:保级延期/保级不延期/降级的用户  -- 昨日该等级的用户
  - 有昨日该等级的有效期，用于有效期判断

用户等级变更类型:
- 01:升级 = 目标用户类型01 + 满足条件
- 02:保级延期 = 目标用户类型02 + 满足条件
- 03:保级不延期 = 目标用户类型02 + 不满足条件 + 未到期
- 04:降级 = 目标用户类型02 + 不满足条件 + 到期 + 降级去向

** AARRR

仍旧使用目标用户来进行判断，只是规则有所不同：
- 新用户：之前是全量用户，现在为排除掉昨日结果表中的用户
- 无条件符合：排除掉获客等级的用户外，其他所有用户
- 其他等级用户：该模型的所有用户参与计算，包含昨日和当日的

无条件符合： 参与计算 + 无规则配置

依赖于生命周期，但并非强依赖，如果获取阶段，配置了生命周期等级，则有，否则无。配置与否，根据业务场景来决定

计算逻辑：
1. 获取目标用户
2. 根据规则，计算出各指标数据，存到指标临时表
3. 根据判断规则，更新到结果表
    1. 无条件符合：未计算过的用户，更新为对应等级
    2. 条件符合：更新为对应等级，按计算顺序更新

** RFM

与其他模型的计算方式保持一致，源表结构设计不一致，考虑映射转换。没有等级信息，但转换为json时也按同样的层级处理

规则json为统一的接口，不一致不满足的，进行处理，保持json格式一致，以便后续处理（进行处理）

指标计算保持一致，将相关信息处理到一起，以使用统计计算逻辑处理

计算逻辑：
1. 目标用户为全量用户
2. 规则判断，若满足为1，不满足为0
3. 输出结果

** GMV (将GMV合并到LTV中)

指标计算逻辑保持不变，无需判断，只需将各个指标按系数加和起来即可 （等级及条件组按JSON格式指定或置为空，保持统一）

特殊处理：
1. 指标计算时，使用特殊逻辑：不需要关联维度表，直接通过系数进行汇总
2. 直接从窄表中加和后即为最终值，无需创建宽表
3. 仅在月末跑，通过调度配置，代码不作处理
4. 统计周期指定为月末，若非月末，则跳过不处理
5. 有效日期无需处理，当前数据有效日期即为当天，每天只有一条数据

** LTV (将GMV合并到LTV中)

需要两个指标：
1. LTV = LT * APRU
   1. LT: 根据指定的生命周期模型，获取到新手日期和流失日期（高危期即算流失），得到用户寿命，再求所有用户的平均
      1. 在生命周期模型结果表中，增加生命周期结束日期，对比昨日状态来更新
      2. 新手日期：不使用结果表，改为在前端选择一个指标来获取日期
   2. APRU: GMV/用户数 （有交易的） -- 统计周期按月平均，用户数按哪个月的计算？
      1. GMV:根据配置的规则进行指标计算，乘以系数后加和的值 （统计周期：本月，即月初到当前）
      2. 用户数：统计周期范围内有交易的去重用户数
      3. 笔数：有交易的，也一并统计出来
      
用户基数：根据所选生命周期的用户来计算（若生命周期有圈群，则此处也为筛选后的用户） - 强依赖于生命周期计算完成
时间范围：从月初到当前日期（15号跑一次，月末跑一次，若为15号，则算出的gmv需*2，否则使用*1） - 用于估算

* 版本v2与v1的区别
目的：优化处理逻辑，方便扩展，以及排查问题

版本v1痛点：
- 涉及大量sql及复杂的处理逻辑，大量sql加工关联逻辑
- 整体处理流程混乱不清楚，排查数据问题困难
- 扩展性差，维护困难
- 强依赖于数据中台，涉及规则变更需重跑，太麻烦

版本v2优化点：
- 配置化：将配置规则，单独读取并生成json对象，不必直接操作数据查询结果
- 模块化：将单个脚本拆分为多个功能模块，便于维护及使用（规则引擎/计算引擎/判断引擎）
- 通用化：将公共的处理逻辑独立出来，各个模型都可以使用，而不必重复编写核心处理代码
- 清晰化：将计算逻辑重新梳理后清晰化，降低各个部分的耦合
- 对象化：将各个部分代码转化为类对象，主代码中进行实例化
- 项目化：将整个模型计算整合到一起，可扩展到支持类似的计算（动态规则计算）
- 灵活化：支持灵活配置，以及增加分析对象，各种场景均可通用
- 码值映射：通过配置表读取映射表及字段，同时调整相关指标目录
- 临时表管理：按模型动态创建，独立享有临时表，临时表按生命周期管理
  - 并行执行时，不会同时merge一个表，不需要像之前一样加锁处理
- 并行化：优化并行处理逻辑，以及重跑逻辑
- 低耦合：取消对象类与物理表的强绑定关系，灵活模型配置，指标对应物理表从映射表中获取
  - 取消之前一级目录对应到物理表
  - 当前可支持各级目录虚拟，不强绑定到表，而是体现业务关系
  - 表的映射通过底层数据实现
- 重新优化了生命周期的计算逻辑
- 配置数据不依赖于数据中台，自行同步，可随时处理

功能扩展：
- 同一规则组支持不同对象类
- 支持不同分析对象主体，使用不同字段 （指标配置表中的rltnp_dime_fld）

* 数据模型支撑
二期增加对象主体：个人用户/企业用户/企网通用户/收单商户/本地商城商户等

** 标签中心-指标标签目录维护
负责指标码值与表/字段的映射关系，以及关联维度表的字段 （根据维度表字段区分维度表）

前端选择到的是BA0001（指标编码），通过该表映射到相关信息：
- 来源物理表
- 来源物理字段
- 指标类型： lab/indx 计算方式不一样
- 关联维度表字段

指标目录调整，目录层级体现业务关系，不绑定到具体表，指标对应表及字段通过底层数据实现

** 指标标签计算表-日表（对象主体维度+其他维度）
- 对象主体维度，用于关联当事人维度表
- 其他维度，用于过滤筛选（如产品筛选/活动筛选）

之前使用的是视图转换，现在改为映射表关联映射，获取对应的字段及来源表

关联取出后放到json对象中后续使用。以此来支持同一规则组中不限对象类，规则组中的各个指标单独计算

** 维度表（对象主体/当事人）
即分析对象，全量 （用户信息表/商户信息表等）

** 圈群-视图
所有圈群关系放到同一个视图中进行查询，简化从不同地方取  v_group_rltnp

通过obj_col区分出关联字段 （即对象字段，拼接到查询中）

* 规则配置说明
各个模型的规则，由业务人员根据业务场景控制，规则间的逻辑关系，以及各指标的周期维度，均由业务人员根据业务场景控制，前后端不作验证及控制。

1. 分等级设置，部分等级不参与计算，即无条件满足 （无规则）
2. 规则通过规则组方式组合（分两种：累加组/规则组）
   1) 累加组：多个同类指标分别统计后加和，再进行判断
   2) 规则组：每个指标规则单独计算后判断，多个规则通过and/or组合
   3) 同一规则组，标签主题必须一致
      + 若需要不同标签主题的，新增不同的规则组
   4) 同一累加组，必须是同类指标，加和有意义
3. 无条件符合： 参与计算，且没有配置规则，则为1-是 （前端无选项） - 自动更新
4. 获取新用户： 默认第一阶段为1-是，其他均为2-否 （前端无选项） - 默认值
5. 参与计算： 默认为参与计算，前端功能选项。取消参与计算，且没有配置规则，无条件符合：2-否
   1) 1-是 2-否
   2) 前端勾选，配合规则配置，判断是否无条件符合
6. 规则： 对象类型-》对象细类-》01-单对象/02-圈群/04-指标标签
   1) 对象类型： 01-产品 02-用户 03-活动
      + 对应到指标目录中的一级目录分类，选择到的指标只能为日表中的，不能将周期汇总的指标加到标签目录中
   2) 对象细类： 积分/权益/产品 （产品）， 活动和用户没有值
   3) 统计周期： mm/dd 指定指标的区间范围
   4) 过滤标签： 标签编号和判断符号和值 -- 用于数据过滤 （可为空）
   5) 具体的指标和判断规则
   6) 选择的指标为基础指标，并非最终指标，需要根据配置规则现算 （模型中用的指标都是这种指标） -- 根据分析主体来加工指标 （当前全部为个人客户，后续扩充后会增加其他分析主体，如企业客户/收单商户/本地生活商户等） -- 对应各个指标表中的当事人主体
      + 基础指标： 标签目录只能放日表的基础指标，不能放周期汇总的指标
      + 日表： 必须有分析主体，以及关联对象的维度，可用于过滤筛选
   7) 分析主体： 即所有指标的主键，即第一阶段获取的目标用户

* 支持场景
1. 运营模型计算： 需要包含分析主体，周期汇总表用在Quickbi展示，指标目录分类
2. 运营画像可视化： 作为运营可视化的底表，通过在Hologre中建视图关联，ECharts展示
3. 千人N面： 待定，个人的在原有基础上增加字段，不满足再新增表，增加对公的

* pyodps上传和使用三方包
新版本支持pyodps-pack打包后使用load特性直接即可使用，当前版本不支持

当前版本0.9.3，使用依赖单个文件的方式处理： （每一步不可少）
1. 上传相关py文件，并提交
2. 引用所有使用到的资源
3. sys.path增加路径 （引用资源至工作空间，只需一个，其他都在同一目录）
4. import导入


* 配套改造

** 圈群视图 v_group_rltnp
- 新建视图，包含所有类型的圈群与对象关系，同时包含对象字段 （用于加工sql）

** 标签映射表 adm_opma_pub_lab_map_confi_tab
- rltnp_dime_fld 必须有值，用于指标计算时的维度关联
- lab_dgre_en_nm 指标粒度，即表主键，用于grp_no的关联 (产品类：存在orgnl_prod_no)

指标类型：（指标类型不同，聚合方式不同）
- indx 可选择，可用于计算和判断 （当前可选， todo: 需考虑区分单指标与可聚合指标）
  - indx_agg 可聚合指标
  - indx_alone 单指标
- lab 可选择，可用于计算和判断  （当前可选）
- attr 只能用于筛选过滤 （当前是不可选，后续可考虑放开筛选）

** 表结构及临时表
1. 当天结果表需更新当前等级，为事务表
2. 最终结果表，需更新有效结束日期，为事务表

** 前端规则配置调整
1. 计算优先级：兜底等级放到最后面，不需要调整 （通过排除计算过的用户来实现）
2. 规则中的累加组，每条规则都加上各自计算指标的条件
   1) 统计周期可以选择，只是过滤条件有点问题
3. 规则配置逻辑调整：每条规则分别配置（分开配置周期/过滤条件/圈群等），分开计算
   1) 规则同周期/周对象，由业务人员根据业务场景控制
4. 各模型规则的字段兼容统一，当前有以下不一致的地方： （生命周期与aarrr对比）
   1) 统计周期位置不一样
   2) 等级表及条件表字段不统一，字段命名也不一致 （ar_cd/biz_id, clc_tp/cyc_tp, clc_val/cyc_val）
   3) aarrr有过滤条件，生命周期没有
   4) 表名规范不统一 （grd/dfe 有些有，有些没有）

** 生产数据验证 20241104
判断逻辑与原来的基本保持一致，作了重构以及优化，以便后续扩展，性能及逻辑优化 （计算逻辑及整个程序结构与之前不一样）

*** lfcy
数据量级基本差不多，出入的数据经过排查，是属于当前不满足，正处于保级不延期阶段，所以新计算逻辑出来的数据是已经降级了的，属于正常现象。

*** aarrr
排查了一个，数据量是一样的，应该没什么问题。并且可能更完善一些 （老逻辑没算出留存，新模型计算出）

* 作业调度流程控制

** 正常流程
1. 设置虚拟节点，作为指标加工完成的关键节点，避免重复依赖，连线混乱，看不清楚逻辑
2. 将各个模型计算的流程，分拆到一个do-while节点中循环一次即可（具体逻辑在节点内部），放在整个业务看板中，模型分叉太多看不全，不方便管理
3. 业务看板中，只需看最上层节点，简洁明了，逻辑清晰
4. 依赖于指标加工完成，每天只跑一次

** 回溯流程
1. 是否判断基础指标数据存在：因为都是跑的历史数据，指标数据都是存在的，只是T-1可能会未加工完成 （增加判断，会导致业务流程变得复杂一些，暂时不加）
2. 每类模型的回溯流程都是一个do-while节点，具体逻辑在节点内部
3. 通过回溯任务列表来驱动
4. 不间断循环检测回溯任务运行，执行多次

* 新增功能

** 20240920 新增功能： 支持模型回溯重跑
业务场景： 模型调整后，希望重跑一段时间的数据，只能让数据组手动处理，麻烦且耗时。支持回溯后，业务人员即可根据需要进行回溯

前端： 业务人员选择后，创建回溯任务列表，同时随时显示最新的回溯进度，并在回溯过程中，可终止当前任务

业务规则：
1. 正常批量与回溯任务互不影响： 若有依赖则会出现较大延迟
2. 正常批量必须每天运行（全量），回溯任务只针对部分模型，回溯可更新批量计算出的结果数据
3. 回溯执行时，先获取最新的规则数据，使用最新规则执行
4. 同一模型同一时间只能有一个回溯中的任务，不同模型可并行（多套回溯流程）
5. 回溯时间范围：当前日期-1，而非任务创建日期-1 (T-1)，保证回溯到最新数据
6. 回溯过程中，可终止回溯，状态为终止中，待当前日期执行完，再更新为终止完成 -- 延迟性
7. 终止中的模型可重新创建回溯任务，不影响，但需等待执行

处理逻辑：
1. 改造当前处理程序，增加参数is_trace，区分正常任务与回溯任务
   1. 使用不同临时表，互不影响，可同时运行
   2. 回溯任务增加逻辑处理与判断，通过任务表驱动执行，每次取一条待回溯任务
   3. 任务获取及任务状态更新，直接在处理程序中增加
   4. 支持终止任务，每次执行之前先检查任务状态，若状态为终止中，则停止后续日期的执行，同时更新状态
   5. 动态获取日期范围，由for改为while true，以支持T-1需求
   6. 处理开始及处理完成，都对任务表状态进行更新
2. do-while回溯流程控制 （按模型分类创建不同的流程，以满足互不影响）
   1. 同步任务表数据至mc中，覆盖写 （因赋值节点中的python无法使用pymysql）
   2. 赋值节点：获取当前待回溯任务数，按模型分类 （无法直联mysql，改为先同步再获取任务数）
   3. 分支节点：任务数=0 与 任务数>0，走不同分支下游 -- 控制命名规范
      1. 有任务：同步最新规则数据，处理流程
         1. 处理流程包含检查及回溯日期处理，以及状态更新，直接mysql操作
         2. 区别于正常批量，传入不同的参数
         3. 问题：pyodps无法使用第三方包pymysql  -- 已解决
      2. 无任务：赋值节点，控制循环等待时间，可自定义时分秒
   4. end节点：循环控制，最大次数 （23点或达到最大循环次数）
3. 整个流程依赖于上一周期-本节点，循环任务结束后再启动循环，会有少量的循环任务堆积
   1. 每天凌晨6点开启循环任务，23点结束，指定启动时间，减少循环次数
4. do-while 数据同步流程控制 （不再需要，来回同步太麻烦，改为直接操作mysql）
   1. 定时获取ob任务表，更新到mc中（delta table），写入前保留现有数据
   2. 定时将mc中的任务表，同步到ob中，供前端查询 （若有前端计算字段，则排除掉该字段同步）


遗留问题： （历史数据导致的数据差异）
1. 存在回溯日期前一天的数据，若有用户不满足规则，当前处于保级不延期的状态，新规则执行时，仍处于保级不延期
2. 若没有历史数据，则会为当前满足规则的等级，即升级而非保级


回溯任务状态： 01-待回溯 02-回溯中 03-回溯成功 04-终止中 05-终止完成 06-回溯失败 07-终止失败

回溯流程说明：
1. 若同一类模型存在多个待回溯任务，每次只能处理一个，依次处理

关于模型回溯时，回溯任务列表的状态问题：
1. 将同步回溯任务列表的逻辑单独拆出来，每5min同步一次状态
2. 各回溯流程中使用虚拟节点代替同步逻辑
3. 各回溯流程执行完成后，休眠10min，正好可以完成一次状态同步，以便下次检测时状态为最新
   1. 修改流程，有回溯任务完成后，也需休眠10min，以便完成状态同步，否则结束后立即下次循环，会因状态不同步导致执行报错
   2. 无回溯任务，不变

模型回溯流程图：
[[./trace_flow/model_trace.jpg]]

** 20241021新增功能： 增加有效开始日期/有效结束日期
业务场景： 模型规则更新或回溯后，数据的有效范围会变化，需明确展示出来，以便业务使用时区分数据的有效性

前置：
1. 结果表涉及更新，需重建为事务表
2. 前端模型定义表中增加修改时间，修改时间为空取创建时间
   1. 仅针对模型定义表：规则有修改后，更新定义表中的修改时间
3. 任务表中增加回溯结束日期，用于判断

处理逻辑：
1. 在每个模型计算之前进行检查更新历史有效数据
2. 回溯过程中终止的任务：重指定日期之前，先检查任务状态，终止中-》终止完成
   1. 回溯之前的数据才会更新有效结束日期，之后的数据不会
   2. 回溯过程中终止，则已回溯日期的数据，有效开始日期为回溯日期，有效结束日期为最大。未回溯日期的数据，仍保持原来的不变
   3. 终止后未回溯任务，有效开始日期为原来的，不是回溯日期
3. 状态变更任务： 停用一段时间后，再次启动
   1. 状态判断： 历史数据量>0 且 昨日数据量=0
   2. 更新历史有效数据，有效结束日期为历史中最大的数据日期
4. 新增逻辑：正常执行写目标表时处理
   1. 有效开始日期：
      1. 若为回溯任务，则取回溯日期 -- 回溯中
      2. 若为普通任务（此时回溯已经完成），涉及回溯过与未回溯过，回溯过取回溯日期，未回溯过取修改日期
         1. 回溯完成后修改过：修改日期>最近回溯结束日期，取修改日期
         2. 回溯完成后未修改过：修改日期<最近回溯结束日期，取对应任务中的回溯日期
         3. 未回溯过：取修改日期 （不管修改与否，都取修改日期）
         4. 回溯过，但重跑的是回溯之前的日期，则取修改日期
   2. 有效结束日期： 统一默认为99991231
5. 更新逻辑：正常执行之前处理判断，更新失效数据
   1. 判断是否需要更新，且仅在第一天时进行更新
      1. 停用后启用任务：昨日数据量=0且昨日之前数据量>0，更新为历史最大日期
      2. 回溯任务：当前批量日期 = 回溯日期，更新为回溯日期
      3. 普通任务：当前批量日期 = 修改日期，更新为修改日期
   2. 更新字段：仅更新有效结束日期，有效开始日期不变，将历史有效数据置为无效数据
   3. 数据范围：历史有效数据，即statt_dt<当前批量日期 and end_date=99991231

** 20241120 新逻辑： 更新定义表中的数据最新日期
在定义表中展示当前最新的数据日期，即跑批日期。前端更新需去查结果表，改为跑批时直接进行更新

前端： 定义表中增加字段-数据最新日期

处理逻辑： 由数据端处理更新，在指定日期完成后，直连mysql更新

** 20250324 新增功能： 增加模型对象分类及用户群编号 model_obj_cl grp_id
模型对象分类： 01-个人用户 02-企业用户 03-企网通用户 04-收单商户 05-本地商城商户 （用于区分不同用户对象）-- lfcy/aarrr/rfm 三个有
用户群编号：当前只有生命周期有，其他没有 （使用后缀为_grp的表）

前置：定义表中增加字段：model_obj_cl grp_id，结果表中增加字段：model_obj_cl

处理逻辑：
1. 调整模型，增加字段
2. 调整生成json的逻辑，新增字段处理
3. 获取目标用户时，根据对象类型从对应表中获取用户
4. 新增_grp表，将当天用户，根据是否有限制用户群，单独存入_grp表中，从该表中获取用户 （rule_evaluator中新增处理逻辑）
5. 调整各个模型对象类型的映射表

* API文档
见branch: gh_pages，上传命令为：`ghp-import -n -p -f build/html`

文档使用sphinx生成
