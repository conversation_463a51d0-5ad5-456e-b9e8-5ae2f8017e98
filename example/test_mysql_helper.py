'''
Author: <PERSON><PERSON><PERSON> He
Date: 2024-11-20 09:31:12
LastEditors: <EMAIL>
LastEditTime: 2024-11-20 10:45:39
FilePath: /opr_model_v2/example/test_mysql_helper.py
Description: MySQLHelper测试示例

Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
'''
import sys
import os

##@resource_reference{"utils.py"}
##@resource_reference{"pymysql.zip"}

# 引入资源至工作空间
sys.path.append(os.path.dirname(os.path.abspath('utils.py')))
# 导入pymysql三方包 （改名pymysql.zip，引用后直接append到sys.path即可使用）
# 只需导入一次，上面的即可，不用每次都append
# sys.path.append("pymysql.zip")

from utils import MySQLHelper, log_message, mysql_config

def test_mysql_operations():
    """测试MySQL基本操作"""

    try:
        # 初始化MySQL助手
        mysql_helper = MySQLHelper(**mysql_config)

        # 测试创建表
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS test_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50),
            age INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        mysql_helper.execute_update(create_table_sql)
        log_message("测试表创建成功")

        # 测试插入单条数据
        insert_sql = "INSERT INTO test_users (name, age) VALUES (%s, %s)"
        mysql_helper.execute_update(insert_sql, ('张三', 25))
        log_message("单条数据插入成功")

        # 测试批量插入数据
        batch_data = [
            ('李四', 30),
            ('王五', 35),
            ('赵六', 40)
        ]
        mysql_helper.execute_many(insert_sql, batch_data)
        log_message("批量数据插入成功")

        # 测试查询数据
        select_sql = "SELECT * FROM test_users"
        results = mysql_helper.execute_query(select_sql)
        log_message("查询结果:")
        for row in results:
            log_message(f"ID: {row[0]}, 姓名: {row[1]}, 年龄: {row[2]}, 创建时间: {row[3]}")

        # 测试更新数据
        update_sql = "UPDATE test_users SET age = age + 1 WHERE name = %s"
        mysql_helper.execute_update(update_sql, ('张三',))
        log_message("数据更新成功")

        # 测试删除数据
        delete_sql = "DELETE FROM test_users WHERE name = %s"
        mysql_helper.execute_update(delete_sql, ('赵六',))
        log_message("数据删除成功")

    except Exception as e:
        log_message(f"测试过程中出现错误: {e}", 'error')

if __name__ == "__main__":
    test_mysql_operations()
