'''
Author: <PERSON><PERSON><PERSON> He
Date: 2024-10-22 09:06:55
LastEditors: <EMAIL>
LastEditTime: 2024-10-22 09:09:56
FilePath: /opr_model_v2/example/test.py
Description: pyodps引用其他文件/包的示例程序

Copyright (c) 2024 by <PERSON><PERSON><PERSON>, All Rights Reserved.
'''
##@resource_reference{"model.py"}

import sys
import os

"""
1. 先引用资源，相关的都需要引用 (单文件，新版本有更好办法，当前版本似乎不支持tar.gz三方包)
2. 路径加到sys.path
3. 再import
（当前版本三方包暂时未通，新版本可直接引用，不需要一个个导入文件） -- 后面可将整个项目打包，引用一次即可
"""

sys.path.append(os.path.dirname(os.path.abspath('model.py'))) #引入资源至工作空间。

import model
print(dir(model))


sql = " SELECT ar_cd AS model_id, nm AS model_nm FROM v2_t_user_aa_rrr "

reader = o.execute_sql(sql).open_reader()

print(dir(reader))

